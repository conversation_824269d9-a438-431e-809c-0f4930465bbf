#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线时间戳详解演示
展示K线数据中时间戳的格式、转换和使用方式
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class KlineTimestampDemo:
    """K线时间戳演示类"""
    
    def __init__(self):
        self.interval_seconds = {
            '1m': 60,
            '3m': 180,
            '5m': 300,
            '15m': 900,
            '30m': 1800,
            '1h': 3600,
            '4h': 14400,
            '6h': 21600,
            '8h': 28800,
            '12h': 43200,
            '1d': 86400,
            '1w': 604800
        }
    
    def demonstrate_raw_timestamp_format(self):
        """演示原始时间戳格式"""
        logger.info("📡 原始API时间戳格式")
        logger.info("=" * 60)
        
        # 模拟CoinGlass API返回的原始时间戳
        raw_timestamps = [
            1704067200000,  # 2024-01-01 00:00:00 UTC
            1704070800000,  # 2024-01-01 01:00:00 UTC
            1704074400000,  # 2024-01-01 02:00:00 UTC
            1704078000000,  # 2024-01-01 03:00:00 UTC
            1704081600000   # 2024-01-01 04:00:00 UTC
        ]
        
        logger.info("🔍 原始时间戳分析:")
        logger.info(f"  格式: 毫秒级Unix时间戳 (13位数字)")
        logger.info(f"  示例: {raw_timestamps[0]}")
        logger.info(f"  数据类型: 整数 (int64)")
        logger.info(f"  时区: UTC (协调世界时)")
        
        logger.info("\n📅 时间戳转换示例:")
        for i, ts in enumerate(raw_timestamps):
            # 转换为datetime对象
            dt = pd.to_datetime(ts, unit='ms')
            
            # 转换为本地时间（假设东八区）
            local_dt = dt + timedelta(hours=8)
            
            logger.info(f"  {ts} → {dt.strftime('%Y-%m-%d %H:%M:%S')} UTC → {local_dt.strftime('%Y-%m-%d %H:%M:%S')} +8")
        
        return raw_timestamps
    
    def demonstrate_pandas_conversion(self):
        """演示pandas时间戳转换"""
        logger.info("\n🐼 Pandas时间戳转换")
        logger.info("=" * 60)
        
        # 创建示例数据
        raw_data = [
            {"time": 1704067200000, "open": "42150.5", "close": "42280.8"},
            {"time": 1704070800000, "open": "42280.8", "close": "42350.2"},
            {"time": 1704074400000, "open": "42350.2", "close": "42420.1"}
        ]
        
        logger.info("🔄 转换过程:")
        logger.info("1. 原始数据 (API响应):")
        for data in raw_data:
            logger.info(f"   {data}")
        
        # 转换为DataFrame
        df = pd.DataFrame(raw_data)
        logger.info(f"\n2. 初始DataFrame:")
        logger.info(f"   time列类型: {df['time'].dtype}")
        logger.info(f"   time列示例: {df['time'].iloc[0]}")
        
        # 时间戳转换
        df['time'] = pd.to_datetime(df['time'], unit='ms')
        logger.info(f"\n3. 转换后DataFrame:")
        logger.info(f"   time列类型: {df['time'].dtype}")
        logger.info(f"   time列示例: {df['time'].iloc[0]}")
        
        # 设置为索引
        df.set_index('time', inplace=True)
        logger.info(f"\n4. 设置索引后:")
        logger.info(f"   索引类型: {type(df.index)}")
        logger.info(f"   索引名称: {df.index.name}")
        logger.info(f"   时区信息: {df.index.tz}")
        
        logger.info(f"\n📊 最终DataFrame结构:")
        logger.info(f"{df}")
        
        return df
    
    def demonstrate_timestamp_calculations(self):
        """演示时间戳计算"""
        logger.info("\n🧮 时间戳计算演示")
        logger.info("=" * 60)
        
        # 当前时间
        current_time = datetime.now()
        current_timestamp_ms = int(current_time.timestamp() * 1000)
        
        logger.info(f"📅 当前时间信息:")
        logger.info(f"  当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"  Unix时间戳(秒): {int(current_time.timestamp())}")
        logger.info(f"  Unix时间戳(毫秒): {current_timestamp_ms}")
        
        # 不同周期的K线时间戳计算
        logger.info(f"\n⏰ 不同周期K线时间戳:")
        
        for interval, seconds in self.interval_seconds.items():
            # 计算当前周期的开始时间
            current_seconds = int(current_time.timestamp())
            period_start_seconds = (current_seconds // seconds) * seconds
            period_start_time = datetime.fromtimestamp(period_start_seconds)
            period_start_ms = period_start_seconds * 1000
            
            # 计算下一个周期的开始时间
            next_period_start_seconds = period_start_seconds + seconds
            next_period_start_time = datetime.fromtimestamp(next_period_start_seconds)
            
            logger.info(f"  {interval:3} 周期:")
            logger.info(f"    当前周期开始: {period_start_time.strftime('%Y-%m-%d %H:%M:%S')} ({period_start_ms})")
            logger.info(f"    下个周期开始: {next_period_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"    周期长度: {seconds}秒")
            
            if interval in ['1h', '4h', '1d']:  # 只显示几个主要周期
                logger.info("")
    
    def demonstrate_kline_completeness_timing(self):
        """演示K线完整性时间判定"""
        logger.info("\n🎯 K线完整性时间判定")
        logger.info("=" * 60)
        
        # 模拟不同的K线场景
        current_time = datetime.now()
        
        scenarios = [
            {
                "name": "1小时K线 - 刚开始",
                "interval": "1h",
                "kline_start": current_time.replace(minute=0, second=0, microsecond=0),
                "current": current_time.replace(minute=5, second=30, microsecond=0)
            },
            {
                "name": "1小时K线 - 进行中", 
                "interval": "1h",
                "kline_start": current_time.replace(minute=0, second=0, microsecond=0),
                "current": current_time.replace(minute=35, second=15, microsecond=0)
            },
            {
                "name": "1小时K线 - 即将完成",
                "interval": "1h", 
                "kline_start": current_time.replace(minute=0, second=0, microsecond=0),
                "current": current_time.replace(minute=58, second=45, microsecond=0)
            },
            {
                "name": "1小时K线 - 已完成",
                "interval": "1h",
                "kline_start": current_time.replace(minute=0, second=0, microsecond=0) - timedelta(hours=1),
                "current": current_time.replace(minute=5, second=30, microsecond=0)
            }
        ]
        
        for scenario in scenarios:
            logger.info(f"\n📊 {scenario['name']}:")
            
            kline_start = scenario['kline_start']
            current = scenario['current']
            interval_seconds = self.interval_seconds[scenario['interval']]
            
            # 计算预期结束时间
            expected_end = kline_start + timedelta(seconds=interval_seconds)
            
            # 计算已过时间和剩余时间
            elapsed = current - kline_start
            elapsed_seconds = elapsed.total_seconds()
            remaining = expected_end - current
            remaining_seconds = max(0, remaining.total_seconds())
            
            # 计算完成百分比
            completion_pct = min(100, (elapsed_seconds / interval_seconds) * 100)
            
            # 判断是否完整
            is_complete = current >= expected_end
            
            logger.info(f"  K线开始时间: {kline_start.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"  当前时间: {current.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"  预期结束时间: {expected_end.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"  已过时间: {elapsed_seconds:.0f}秒 ({elapsed_seconds/60:.1f}分钟)")
            logger.info(f"  剩余时间: {remaining_seconds:.0f}秒 ({remaining_seconds/60:.1f}分钟)")
            logger.info(f"  完成度: {completion_pct:.1f}%")
            logger.info(f"  是否完整: {'✅ 是' if is_complete else '❌ 否'}")
            
            # 时间戳格式
            kline_start_ms = int(kline_start.timestamp() * 1000)
            current_ms = int(current.timestamp() * 1000)
            expected_end_ms = int(expected_end.timestamp() * 1000)
            
            logger.info(f"  时间戳信息:")
            logger.info(f"    开始: {kline_start_ms}")
            logger.info(f"    当前: {current_ms}")
            logger.info(f"    结束: {expected_end_ms}")
    
    def demonstrate_timezone_handling(self):
        """演示时区处理"""
        logger.info("\n🌍 时区处理演示")
        logger.info("=" * 60)
        
        # 原始UTC时间戳
        utc_timestamp_ms = 1704067200000  # 2024-01-01 00:00:00 UTC
        
        logger.info(f"🕐 原始时间戳: {utc_timestamp_ms}")
        
        # 转换为不同时区的时间
        utc_time = pd.to_datetime(utc_timestamp_ms, unit='ms')
        
        # 常见时区转换
        timezones = {
            'UTC': utc_time,
            'UTC+8 (北京)': utc_time + timedelta(hours=8),
            'UTC-5 (纽约)': utc_time - timedelta(hours=5),
            'UTC+1 (伦敦)': utc_time + timedelta(hours=1),
            'UTC+9 (东京)': utc_time + timedelta(hours=9)
        }
        
        logger.info(f"\n🌐 时区转换:")
        for tz_name, tz_time in timezones.items():
            logger.info(f"  {tz_name:15}: {tz_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        logger.info(f"\n⚠️ 重要提醒:")
        logger.info(f"  - CoinGlass API返回的时间戳都是UTC时间")
        logger.info(f"  - 系统内部统一使用UTC时间进行计算")
        logger.info(f"  - 显示给用户时可以转换为本地时间")
        logger.info(f"  - K线完整性判定必须使用UTC时间")
    
    def get_timestamp_summary(self):
        """获取时间戳总结"""
        logger.info("\n📋 K线时间戳总结")
        logger.info("=" * 60)
        
        summary_info = [
            "🔢 格式: 13位毫秒级Unix时间戳",
            "🌍 时区: UTC (协调世界时)",
            "📊 数据类型: int64 → datetime64[ns]",
            "🔄 转换方法: pd.to_datetime(timestamp, unit='ms')",
            "📅 索引类型: DatetimeIndex",
            "⏰ 精度: 毫秒级 (实际使用分钟级)",
            "🎯 用途: K线开始时间标记",
            "🔍 完整性判定: 当前时间 vs 预期结束时间",
            "📈 周期计算: 开始时间 + 周期秒数 = 结束时间",
            "⚠️ 注意: 未完成K线的时间戳指向开始时间"
        ]
        
        for info in summary_info:
            logger.info(f"  {info}")


def main():
    """主演示函数"""
    logger.info("🎯 K线时间戳详解演示")
    logger.info("=" * 80)
    
    demo = KlineTimestampDemo()
    
    # 1. 原始时间戳格式
    demo.demonstrate_raw_timestamp_format()
    
    # 2. Pandas转换
    demo.demonstrate_pandas_conversion()
    
    # 3. 时间戳计算
    demo.demonstrate_timestamp_calculations()
    
    # 4. 完整性判定
    demo.demonstrate_kline_completeness_timing()
    
    # 5. 时区处理
    demo.demonstrate_timezone_handling()
    
    # 6. 总结
    demo.get_timestamp_summary()
    
    logger.info("\n✅ K线时间戳详解完成")
    logger.info("=" * 80)


if __name__ == "__main__":
    main()
