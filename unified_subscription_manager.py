#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一订阅管理器 - 使用user_data.json作为唯一数据源
整合了原本分散的三个订阅管理器功能，确保数据一致性和正确的信号推送
"""

import json
import os
import asyncio
import logging
import platform
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any
from pathlib import Path

# 跨平台系统检测
SYSTEM_TYPE = platform.system().lower()

logger = logging.getLogger(__name__)

# 配置文件路径
USER_DATA_FILE = "data/user_data.json"
DATA_DIR = "data"

def get_beijing_time():
    """获取北京时间"""
    beijing_tz = timezone(timedelta(hours=8))
    return datetime.now(beijing_tz)

def beijing_time_isoformat():
    """返回北京时间ISO格式字符串"""
    return get_beijing_time().isoformat()

def format_beijing_time(time_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化北京时间"""
    try:
        if time_str.endswith('+08:00'):
            dt = datetime.fromisoformat(time_str)
        else:
            dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            dt = dt.astimezone(timezone(timedelta(hours=8)))
        return dt.strftime(format_str)
    except Exception:
        return time_str

class DataManager:
    """数据管理器 - 简化版本，专门用于订阅管理"""
    
    @staticmethod
    def load_json(file_path: str, default_value=None):
        """安全加载JSON文件 - 防止用户数据丢失"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 🚨 特殊检查：如果是用户数据文件且为空，拒绝返回空数据
                if file_path.endswith('user_data.json'):
                    if not isinstance(data, dict) or len(data) == 0:
                        logger.error(f"❌ 用户数据文件为空或无效: {file_path}")
                        raise ValueError(f"用户数据文件无效: 类型={type(data)}, 长度={len(data) if isinstance(data, dict) else 'N/A'}")

                return data
            else:
                # 🚨 特殊处理：如果是用户数据文件不存在，抛出异常
                if file_path.endswith('user_data.json'):
                    logger.error(f"❌ 用户数据文件不存在: {file_path}")
                    raise FileNotFoundError(f"用户数据文件不存在: {file_path}")

        except Exception as e:
            # 🚨 对于用户数据文件，不要返回默认值，而是抛出异常
            if file_path.endswith('user_data.json'):
                logger.error(f"❌ 用户数据文件加载失败，拒绝返回空数据: {e}")
                raise e
            else:
                logger.error(f"加载JSON文件失败 {file_path}: {e}")

        return default_value if default_value is not None else {}
    
    @staticmethod
    def save_json(file_path: str, data):
        """安全保存JSON文件 - 防止数据丢失"""
        try:
            # 🚨 特殊验证：如果是用户数据文件，进行严格检查
            if file_path.endswith('user_data.json'):
                if not isinstance(data, dict):
                    logger.error(f"❌ 拒绝保存非字典类型的用户数据: {type(data)}")
                    return False

                if len(data) == 0:
                    logger.error(f"❌ 拒绝保存空的用户数据")
                    return False

                # 检查现有文件，如果新数据明显少于现有数据，发出警告
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            existing_data = json.load(f)
                        if isinstance(existing_data, dict) and len(existing_data) > 0:
                            if len(data) < len(existing_data) * 0.3:  # 新数据少于30%
                                logger.error(f"❌ 新用户数据异常减少 {len(existing_data)} -> {len(data)}，拒绝保存")
                                return False
                    except Exception as e:
                        logger.warning(f"⚠️ 无法读取现有数据进行比较: {e}")

            Path(file_path).parent.mkdir(parents=True, exist_ok=True)

            # 原子性写入：先写临时文件，再重命名
            temp_file = file_path + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # 验证写入的文件
            with open(temp_file, 'r', encoding='utf-8') as f:
                json.load(f)  # 验证JSON格式

            # 原子性重命名 - 跨平台兼容
            if SYSTEM_TYPE == 'windows':  # Windows
                if os.path.exists(file_path):
                    os.remove(file_path)
                os.rename(temp_file, file_path)
            else:  # Linux/Unix - 支持原子性重命名
                os.rename(temp_file, file_path)

            return True
        except Exception as e:
            logger.error(f"保存JSON文件失败 {file_path}: {e}")
            # 清理临时文件
            temp_file = file_path + '.tmp'
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            return False
    
    @staticmethod
    def get_user_data(user_id: int, file_path: str = USER_DATA_FILE) -> Dict:
        """安全获取用户数据"""
        try:
            all_data = DataManager.load_json(file_path, {})
            return all_data.get(str(user_id), {})
        except Exception as e:
            logger.error(f"❌ 获取用户数据失败 user_id={user_id}: {e}")
            # 对于用户数据文件，不返回空字典，而是抛出异常
            if file_path.endswith('user_data.json'):
                raise e
            return {}
    
    @staticmethod
    def update_user_data(user_id: int, user_data: Dict, file_path: str = USER_DATA_FILE) -> bool:
        """安全更新用户数据"""
        try:
            # 验证用户数据不为空
            if not user_data or not isinstance(user_data, dict):
                logger.error(f"❌ 拒绝更新空的用户数据 user_id={user_id}")
                return False

            all_data = DataManager.load_json(file_path, {})
            all_data[str(user_id)] = user_data

            # 验证更新后的数据不为空
            if not all_data or len(all_data) == 0:
                logger.error(f"❌ 更新后的数据为空，拒绝保存 user_id={user_id}")
                return False

            return DataManager.save_json(file_path, all_data)
        except Exception as e:
            logger.error(f"❌ 更新用户数据失败 user_id={user_id}: {e}")
            return False

class UnifiedSubscriptionManager:
    """统一订阅管理器 - 使用user_data.json作为唯一数据源"""
    
    DAILY_COST = 50  # 每日消耗积分
    
    def __init__(self, user_data_file: str = USER_DATA_FILE):
        self.user_data_file = user_data_file
        self._ensure_data_file()
        
        # 权限缓存 - 避免重复扣费
        self._permission_cache = {}
        self._cache_timestamp = None
        self.CACHE_DURATION = 300  # 5分钟缓存
        
        logger.info(f"✅ 统一订阅管理器初始化完成，数据源: {self.user_data_file}")
    
    def _ensure_data_file(self):
        """确保数据文件存在 - 安全版本"""
        Path(self.user_data_file).parent.mkdir(parents=True, exist_ok=True)

        # 🚨 SAFETY: 检查文件是否存在，不自动恢复
        if not Path(self.user_data_file).exists():
            logger.error(f"❌ 用户数据文件不存在: {self.user_data_file}")
            logger.error("❌ 自动恢复已禁用，请手动检查数据文件位置")
            raise FileNotFoundError(f"用户数据文件不存在: {self.user_data_file}")
    
    def get_user_subscription_status(self, user_id: int) -> Dict:
        """获取用户订阅状态"""
        user_data = DataManager.get_user_data(user_id, self.user_data_file)
        points = user_data.get("points", 0)
        subscription_active = user_data.get("subscription_active", False)
        
        # 🔒 安全检查：如果用户主动取消订阅，强制设为非激活状态
        cancelled_by_user = user_data.get("subscription_cancelled_by_user", False)
        if cancelled_by_user:
            subscription_active = False
        
        # 检查是否需要今日扣费
        today = format_beijing_time(get_beijing_time().isoformat(), "%Y-%m-%d")
        subscription = user_data.get("subscription", {})
        last_charge = subscription.get("last_charge_date")
        
        # 修正：只有用户主动订阅且积分足够才认为激活
        is_active = subscription_active and points >= self.DAILY_COST and not cancelled_by_user
        days_remaining = points // self.DAILY_COST if points >= self.DAILY_COST else 0
        
        # 修正：需要扣费的条件 - 用户已订阅、今天未扣费、积分足够、未被用户取消
        needs_charge_today = (subscription_active and 
                            last_charge != today and 
                            points >= self.DAILY_COST and
                            not cancelled_by_user)
        
        return {
            "is_active": is_active,
            "points": points,
            "subscription_active": subscription_active,
            "cancelled_by_user": cancelled_by_user,
            "last_charge_date": last_charge,
            "days_remaining": days_remaining,
            "needs_charge_today": needs_charge_today
        }
    
    def process_daily_charge(self, user_id: int) -> Dict:
        """处理每日扣费"""
        user_data = DataManager.get_user_data(user_id, self.user_data_file)
        today = format_beijing_time(get_beijing_time().isoformat(), "%Y-%m-%d")
        
        subscription = user_data.setdefault("subscription", {})
        
        # 🔒 安全检查1: 检查用户是否主动取消订阅
        if user_data.get('subscription_cancelled_by_user', False):
            logger.debug(f"🔒 用户 {user_id} 已主动取消订阅，拒绝扣费")
            return {
                "success": False,
                "message": "用户已取消订阅",
                "points_after": user_data.get("points", 0),
                "charged": False
            }
        
        # 🔒 安全检查2: 检查订阅是否激活
        if not user_data.get('subscription_active', False):
            logger.debug(f"🔒 用户 {user_id} 订阅未激活，拒绝扣费")
            return {
                "success": False,
                "message": "订阅未激活",
                "points_after": user_data.get("points", 0),
                "charged": False
            }
        
        # 检查是否已扣费
        if subscription.get("last_charge_date") == today:
            return {
                "success": True, 
                "message": "今日已扣费",
                "points_after": user_data.get("points", 0),
                "charged": False
            }
        
        # 检查积分是否足够
        points = user_data.get("points", 0)
        if points < self.DAILY_COST:
            subscription["is_active"] = False
            subscription["last_charge_date"] = today
            DataManager.update_user_data(user_id, user_data, self.user_data_file)
            
            return {
                "success": False,
                "message": f"积分不足，需要{self.DAILY_COST}积分，当前{points}积分",
                "points_after": points,
                "charged": False
            }
        
        # 扣除积分
        new_points = points - self.DAILY_COST
        user_data["points"] = new_points
        user_data["last_active"] = beijing_time_isoformat()
        
        # 更新订阅状态
        subscription.update({
            "is_active": True,
            "last_charge_date": today,
            "daily_cost": self.DAILY_COST,
            "auto_renew": True
        })
        
        # 保存数据
        DataManager.update_user_data(user_id, user_data, self.user_data_file)
        
        logger.info(f"用户 {user_id} 每日扣费成功: -{self.DAILY_COST}积分，余额: {new_points}")
        
        return {
            "success": True,
            "message": f"扣费成功，消耗{self.DAILY_COST}积分",
            "points_before": points,
            "points_after": new_points,
            "charged": True
        }
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if self._cache_timestamp is None:
            return False
        
        current_time = datetime.now().timestamp()
        return (current_time - self._cache_timestamp) < self.CACHE_DURATION
    
    def check_service_access(self, user_id: int) -> bool:
        """检查用户服务权限 - 带缓存和自动扣费"""
        try:
            # 🔒 安全检查：首先验证用户是否主动取消订阅
            user_data = DataManager.get_user_data(user_id, self.user_data_file)
            if user_data.get('subscription_cancelled_by_user', False):
                logger.debug(f"🔒 用户 {user_id} 已主动取消订阅，拒绝访问")
                return False
            
            # 检查基本订阅状态
            if not user_data.get('subscription_active', False):
                logger.debug(f"🔒 用户 {user_id} 订阅未激活，拒绝访问")
                return False
            
            # 检查缓存是否有效
            if not self._is_cache_valid():
                self._refresh_permission_cache()
            
            # 从缓存获取权限
            if user_id in self._permission_cache:
                is_active = self._permission_cache[user_id]
                logger.debug(f"用户 {user_id} 服务权限 (缓存): {is_active}")
                return is_active
            
            # 缓存中没有该用户，单独检查（新用户）
            logger.debug(f"用户 {user_id} 不在缓存中，单独检查权限...")
            status = self.get_user_subscription_status(user_id)
            
            # 🔧 修复：只在明确需要扣费时才扣费，避免重复扣费
            if status.get("needs_charge_today", False):
                logger.info(f"💰 用户 {user_id} 需要今日扣费，正在处理...")
                charge_result = self.process_daily_charge(user_id)
                
                if charge_result.get("success", False) and charge_result.get("charged", False):
                    logger.info(f"✅ 用户 {user_id} 扣费成功: -{self.DAILY_COST}积分")
                    # 重新获取状态
                    status = self.get_user_subscription_status(user_id)
                elif not charge_result.get("success", False):
                    logger.warning(f"❌ 用户 {user_id} 扣费失败: {charge_result.get('message', '未知原因')}")
                else:
                    logger.debug(f"用户 {user_id} 今日已扣费，无需重复扣费")
            
            is_active = status["is_active"]
            
            # 缓存结果
            self._permission_cache[user_id] = is_active
            
            logger.debug(f"用户 {user_id} 服务权限检查完成: {is_active}")
            return is_active
            
        except Exception as e:
            logger.error(f"❌ 检查用户 {user_id} 服务权限失败: {e}")
            return False
    
    def process_all_daily_charges(self) -> Dict:
        """处理所有用户的每日扣费"""
        try:
            logger.info("💰 开始执行全体用户每日扣费...")
            
            user_data_all = DataManager.load_json(self.user_data_file, {})
            total_processed = 0
            charged_users = 0
            failed_users = 0
            skipped_users = 0
            
            for user_id_str, user_data in user_data_all.items():
                try:
                    user_id = int(user_id_str)
                    total_processed += 1
                    
                    # 获取用户状态
                    status = self.get_user_subscription_status(user_id)
                    
                    # 🔒 安全检查：跳过被用户主动取消订阅的用户
                    if status.get("cancelled_by_user", False):
                        logger.debug(f"🔒 用户 {user_id} 已主动取消订阅，跳过扣费")
                        skipped_users += 1
                        continue
                    
                    # 如果需要扣费，处理扣费
                    if status.get("needs_charge_today", False):
                        logger.debug(f"用户 {user_id} 需要今日扣费，正在处理...")
                        charge_result = self.process_daily_charge(user_id)
                        
                        if charge_result.get("success", False):
                            charged_users += 1
                            logger.debug(f"用户 {user_id} 扣费成功")
                        else:
                            failed_users += 1
                            logger.debug(f"用户 {user_id} 扣费失败: {charge_result.get('message', '未知原因')}")
                    else:
                        skipped_users += 1
                    
                except (ValueError, KeyError) as e:
                    logger.warning(f"跳过无效用户ID: {user_id_str}, 错误: {e}")
                    failed_users += 1
                    continue
            
            logger.info(f"✅ 全体用户扣费完成: 处理{total_processed}个用户，成功扣费{charged_users}个，失败{failed_users}个，跳过{skipped_users}个")
            
            return {
                "success": True,
                "total_processed": total_processed,
                "charged_users": charged_users,
                "failed_users": failed_users,
                "skipped_users": skipped_users
            }
            
        except Exception as e:
            logger.error(f"❌ 全体用户扣费失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "total_processed": 0,
                "charged_users": 0,
                "failed_users": 0,
                "skipped_users": 0
            }

    def _refresh_permission_cache(self):
        """刷新权限缓存 - 一次性检查所有用户权限"""
        try:
            logger.info("🔄 刷新用户权限缓存...")
            
            user_data_all = DataManager.load_json(self.user_data_file, {})
            new_cache = {}
            processed_count = 0
            charged_count = 0
            
            for user_id_str, user_data in user_data_all.items():
                try:
                    user_id = int(user_id_str)
                    
                    # 获取用户状态
                    status = self.get_user_subscription_status(user_id)
                    
                    # 🔒 安全检查：跳过被用户主动取消订阅的用户
                    if status.get("cancelled_by_user", False):
                        logger.debug(f"🔒 用户 {user_id} 已主动取消订阅，跳过扣费")
                        new_cache[user_id] = False
                        processed_count += 1
                        continue
                    
                    # 如果需要扣费，处理扣费
                    if status.get("needs_charge_today", False):
                        logger.debug(f"用户 {user_id} 需要今日扣费，正在处理...")
                        charge_result = self.process_daily_charge(user_id)
                        
                        if charge_result.get("success", False):
                            # 重新获取状态
                            status = self.get_user_subscription_status(user_id)
                            charged_count += 1
                            logger.debug(f"用户 {user_id} 扣费成功")
                        else:
                            logger.debug(f"用户 {user_id} 扣费失败: {charge_result.get('message', '未知原因')}")
                    
                    # 缓存权限结果
                    new_cache[user_id] = status["is_active"]
                    processed_count += 1
                    
                except (ValueError, KeyError) as e:
                    logger.warning(f"跳过无效用户ID: {user_id_str}, 错误: {e}")
                    continue
            
            # 更新缓存
            self._permission_cache = new_cache
            self._cache_timestamp = datetime.now().timestamp()
            
            logger.info(f"✅ 权限缓存刷新完成: 处理{processed_count}个用户，扣费{charged_count}个用户")
            
        except Exception as e:
            logger.error(f"❌ 刷新权限缓存失败: {e}")
    
    def get_active_subscribers(self) -> List[Dict]:
        """获取所有活跃订阅者 - 统一从user_data.json获取"""
        try:
            user_data_all = DataManager.load_json(self.user_data_file, {})
            active_subscribers = []
            
            debug_counts = {
                "total_users": 0,
                "subscription_active": 0,
                "has_subscription_types": 0,
                "sufficient_points": 0,
                "final_active": 0,
                "issues": []
            }
            
            for user_id_str, user_data in user_data_all.items():
                try:
                    user_id = int(user_id_str)
                    username = user_data.get("username", "Unknown")
                    points = user_data.get("points", 0)
                    is_active = user_data.get("subscription_active", False)
                    subscription_types = user_data.get("subscription_types", [])
                    
                    debug_counts["total_users"] += 1
                    
                    if is_active:
                        debug_counts["subscription_active"] += 1
                    
                    if subscription_types:
                        debug_counts["has_subscription_types"] += 1
                    
                    if points >= 50:
                        debug_counts["sufficient_points"] += 1
                    
                    # 🔧 改进的判断逻辑：更宽松的条件
                    should_include = False
                    issue_reason = None
                    
                    if is_active and subscription_types:
                        # 标准情况：已激活且有订阅类型
                        should_include = True
                    elif is_active:
                        # 🎯 简化：只要激活就包含，不再检查subscription_types
                        should_include = True
                    elif not is_active and subscription_types and points >= 50:
                        # 🔒 安全修复：移除自动激活逻辑，尊重用户取消订阅的意愿
                        # 用户主动取消订阅后，不应该被自动重新激活
                        issue_reason = f"用户已取消订阅(积分:{points}, 类型:{subscription_types})"
                        logger.debug(f"🔒 用户 {user_id} 已取消订阅，不会自动激活")
                    else:
                        if not is_active:
                            issue_reason = f"未激活订阅(积分:{points})"
                        elif not subscription_types:
                            issue_reason = "无订阅类型"
                        else:
                            issue_reason = f"未知问题(激活:{is_active}, 类型:{subscription_types}, 积分:{points})"
                    
                    if should_include:
                        # 构建统一的订阅者数据格式
                        subscriber_data = {
                            "user_id": user_id,
                            "username": username,
                            "first_name": username,
                            "is_active": True,
                            "subscription_types": subscription_types if subscription_types else ["ai_signals"],  # 默认AI信号
                            "alerts_received": user_data.get("alerts_received", {
                                "ai": 0, "transfer": 0, "total": 0
                            }),
                            "last_alert": user_data.get("last_alert_time", None),
                            "subscribed_at": user_data.get("register_time", beijing_time_isoformat())
                        }
                        active_subscribers.append(subscriber_data)
                        debug_counts["final_active"] += 1
                        
                        if issue_reason:
                            logger.debug(f"✅ 用户 {user_id} ({username}): {issue_reason}")
                    else:
                        debug_counts["issues"].append(f"用户{user_id}({username}): {issue_reason}")
                        
                except (ValueError, KeyError) as e:
                    logger.warning(f"跳过无效用户数据: {user_id_str}, 错误: {e}")
                    continue
            
            # 详细的调试日志
            logger.info(f"📊 订阅者获取统计:")
            logger.info(f"   - 总用户数: {debug_counts['total_users']}")
            logger.info(f"   - 已激活订阅: {debug_counts['subscription_active']}")
            logger.info(f"   - 有订阅类型: {debug_counts['has_subscription_types']}")  
            logger.info(f"   - 积分充足: {debug_counts['sufficient_points']}")
            logger.info(f"   - 最终活跃订阅者: {debug_counts['final_active']}")
            
            if debug_counts["issues"]:
                logger.warning(f"⚠️ 无法接收信号的用户 ({len(debug_counts['issues'])}个):")
                for issue in debug_counts["issues"][:10]:  # 只显示前10个
                    logger.warning(f"   {issue}")
                if len(debug_counts["issues"]) > 10:
                    logger.warning(f"   ... 还有 {len(debug_counts['issues']) - 10} 个用户")
            
            return active_subscribers
            
        except Exception as e:
            logger.error(f"❌ 获取活跃订阅者失败: {e}")
            return []
    
    def update_alert_stats(self, user_id: int, alert_type: str = "ai"):
        """更新用户信号统计 - 统一在user_data.json中管理"""
        try:
            user_data = DataManager.get_user_data(user_id, self.user_data_file)
            
            # 确保alerts_received是字典格式
            if not isinstance(user_data.get("alerts_received"), dict):
                user_data["alerts_received"] = {"ai": 0, "transfer": 0, "total": 0}
            
            # 更新统计
            alerts_received = user_data["alerts_received"]
            alerts_received[alert_type] = alerts_received.get(alert_type, 0) + 1
            alerts_received["total"] = alerts_received.get("total", 0) + 1
            
            # 更新最后信号时间
            user_data["last_alert_time"] = beijing_time_isoformat()
            
            # 保存更新
            success = DataManager.update_user_data(user_id, user_data, self.user_data_file)
            if success:
                logger.debug(f"✅ 更新用户 {user_id} 信号统计: {alert_type}")
            else:
                logger.error(f"❌ 更新用户 {user_id} 信号统计失败")
                
        except Exception as e:
            logger.error(f"❌ 更新用户 {user_id} 信号统计异常: {e}")
    
    def get_subscription_stats(self) -> Dict:
        """获取订阅统计信息"""
        try:
            user_data_all = DataManager.load_json(self.user_data_file, {})
            
            total_users = len(user_data_all)
            active_subscribers = 0
            total_alerts_sent = 0
            
            for user_data in user_data_all.values():
                if user_data.get("subscription_active", False):
                    active_subscribers += 1
                
                alerts_received = user_data.get("alerts_received", {})
                if isinstance(alerts_received, dict):
                    total_alerts_sent += alerts_received.get("total", 0)
            
            return {
                "total_users": total_users,
                "active_subscribers": active_subscribers,
                "total_alerts_sent": total_alerts_sent,
                "last_updated": beijing_time_isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取订阅统计失败: {e}")
            return {
                "total_users": 0,
                "active_subscribers": 0, 
                "total_alerts_sent": 0,
                "last_updated": beijing_time_isoformat()
            }
    
    def is_subscribed(self, user_id: int) -> bool:
        """检查用户是否已订阅"""
        return self.check_service_access(user_id)
    
    def get_user_subscription_types(self, user_id: int) -> list:
        """获取用户订阅的功能类型"""
        user_data = DataManager.get_user_data(user_id, self.user_data_file)
        return user_data.get('subscription_types', [])
    
    def get_user_details(self, user_id: int) -> Dict:
        """获取用户详细信息"""
        user_data = DataManager.get_user_data(user_id, self.user_data_file)
        return {
            'user_id': user_id,
            'username': user_data.get('username', 'Unknown'),
            'points': user_data.get('points', 0),
            'subscription_active': user_data.get('subscription_active', False),
            'subscription_types': user_data.get('subscription_types', []),
            'alerts_received': user_data.get('alerts_received', {}),
            'last_alert_time': user_data.get('last_alert_time'),
            'register_time': user_data.get('register_time'),
            'last_active': user_data.get('last_active')
        }
    
    def set_subscription_active(self, user_id: int, active: bool) -> bool:
        """设置用户订阅状态"""
        try:
            user_data = DataManager.get_user_data(user_id, self.user_data_file)
            
            if active:
                # 🔒 清除取消订阅标记（用户重新主动订阅）- 必须在扣费之前清除
                if 'subscription_cancelled_by_user' in user_data:
                    del user_data['subscription_cancelled_by_user']
                if 'subscription_cancelled_at' in user_data:
                    del user_data['subscription_cancelled_at']
                
                # 设置订阅状态为激活（扣费前必须设置）
                user_data['subscription_active'] = True
                
                # 先保存清除标记和设置状态后的数据
                DataManager.update_user_data(user_id, user_data, self.user_data_file)
                
                # 检查用户积分是否足够
                points = user_data.get("points", 0)
                if points < self.DAILY_COST:
                    logger.warning(f"用户 {user_id} 积分不足，无法激活订阅: {points} < {self.DAILY_COST}")
                    return False
                
                # 检查今天是否已经扣费
                today = format_beijing_time(get_beijing_time().isoformat(), "%Y-%m-%d")
                subscription = user_data.get("subscription", {})
                last_charge = subscription.get("last_charge_date")
                
                # 激活订阅时立即扣费（除非今天已经扣过费）
                if last_charge != today:
                    logger.info(f"用户 {user_id} 激活订阅，立即扣费 {self.DAILY_COST} 积分")
                    
                    # 立即执行扣费
                    charge_result = self.process_daily_charge(user_id)
                    if not charge_result.get("success", False):
                        logger.error(f"用户 {user_id} 激活订阅时扣费失败: {charge_result}")
                        return False
                    
                    # 重新获取更新后的用户数据
                    user_data = DataManager.get_user_data(user_id, self.user_data_file)
                else:
                    logger.info(f"用户 {user_id} 激活订阅，今日已扣费，直接激活")
                
                # 🎯 简化：激活订阅即代表订阅全部信号
                logger.info(f"用户 {user_id} 开始订阅全部信号")
                
                # 设置默认订阅类型
                user_data['subscription_types'] = ["ai_signals", "transfer_alerts"]
                
            else:
                # 🔒 安全增强：记录用户主动取消订阅，防止意外重新激活
                user_data['subscription_cancelled_by_user'] = True
                user_data['subscription_cancelled_at'] = beijing_time_isoformat()
                logger.info(f"🔒 用户 {user_id} 主动取消订阅，已记录保护标记")
            
            # 设置订阅状态（取消订阅时才需要设置，激活订阅时已经在前面设置过了）
            if not active:
                user_data['subscription_active'] = active
            success = DataManager.update_user_data(user_id, user_data, self.user_data_file)
            
            if success:
                # 清除缓存，强制重新计算权限
                if user_id in self._permission_cache:
                    del self._permission_cache[user_id]
                
                logger.info(f"用户 {user_id} 订阅状态已设置为: {active}")
                return True
            else:
                logger.error(f"用户 {user_id} 订阅状态设置失败")
                return False
            
        except Exception as e:
            logger.error(f"设置用户 {user_id} 订阅状态失败: {e}")
            return False
    
    def get_subscription_active_status(self, user_id: int) -> bool:
        """获取用户订阅激活状态"""
        user_data = DataManager.get_user_data(user_id, self.user_data_file)
        return user_data.get('subscription_active', False)
    
    def get_all_subscription_stats(self) -> Dict:
        """获取所有用户订阅统计信息"""
        try:
            if not os.path.exists(self.user_data_file):
                return {"success": False, "message": "用户数据文件不存在"}
            
            all_data = DataManager.load_json(self.user_data_file, {})
            if not all_data:
                return {"success": False, "message": "无用户数据"}
            
            stats = {
                "total_users": len(all_data),
                "subscribed_users": 0,
                "active_users": 0,
                "total_points": 0,
                "potential_revenue": 0,
                "users_need_charge": 0
            }
            
            today = format_beijing_time(get_beijing_time().isoformat(), "%Y-%m-%d")
            
            for user_id_str, user_data in all_data.items():
                points = user_data.get("points", 0)
                subscription_active = user_data.get("subscription_active", False)
                subscription = user_data.get("subscription", {})
                last_charge = subscription.get("last_charge_date")
                
                stats["total_points"] += points
                
                if subscription_active:
                    stats["subscribed_users"] += 1
                    
                    if points >= self.DAILY_COST:
                        stats["active_users"] += 1
                        stats["potential_revenue"] += self.DAILY_COST
                        
                        if last_charge != today:
                            stats["users_need_charge"] += 1
            
            return {"success": True, "stats": stats}
            
        except Exception as e:
            logger.error(f"获取订阅统计失败: {e}")
            return {"success": False, "message": f"获取统计失败: {str(e)}"}

# 全局实例管理
_global_unified_subscription_manager = None

def get_unified_subscription_manager():
    """获取全局统一订阅管理器实例"""
    global _global_unified_subscription_manager
    if _global_unified_subscription_manager is None:
        _global_unified_subscription_manager = UnifiedSubscriptionManager()
    return _global_unified_subscription_manager

# 兼容性别名 - 方便迁移
IntegratedSubscriptionManager = UnifiedSubscriptionManager
DailySubscriptionManager = UnifiedSubscriptionManager
SignalSubscriptionManager = UnifiedSubscriptionManager

def get_subscription_manager():
    """兼容性函数 - 返回统一订阅管理器"""
    return get_unified_subscription_manager()

if __name__ == "__main__":
    # 测试统一订阅管理器
    manager = UnifiedSubscriptionManager()
    
    # 测试获取订阅者
    subscribers = manager.get_active_subscribers()
    print(f"找到 {len(subscribers)} 个活跃订阅者")
    
    # 测试统计信息
    stats = manager.get_subscription_stats()
    print(f"订阅统计: {stats}")
    
    print("✅ 统一订阅管理器测试完成") 