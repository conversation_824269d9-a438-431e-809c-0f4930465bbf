#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模块K线数据结构详解
展示AI模块使用的K线数据的完整格式、来源和处理流程
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class KlineDataStructureGuide:
    """K线数据结构指南"""
    
    def __init__(self):
        self.data_sources = {
            "futures": "https://open-api-v4.coinglass.com/api/futures/price/history",
            "spot": "https://open-api-v4.coinglass.com/api/spot/price/history"
        }
    
    def demonstrate_raw_api_response(self):
        """演示原始API响应格式"""
        logger.info("📡 原始API响应格式演示")
        logger.info("=" * 60)
        
        # 模拟CoinGlass API的原始响应
        raw_response = {
            "code": "0",
            "msg": "success",
            "data": [
                {
                    "time": 1704067200000,  # 毫秒时间戳
                    "open": "42150.5",      # 开盘价（字符串格式）
                    "high": "42380.2",      # 最高价
                    "low": "42050.1",       # 最低价
                    "close": "42280.8",     # 收盘价
                    "volume_usd": "125680000.50"  # 成交量（美元）
                },
                {
                    "time": 1704070800000,
                    "open": "42280.8",
                    "high": "42450.3",
                    "low": "42180.5",
                    "close": "42350.2",
                    "volume_usd": "98750000.25"
                }
                # ... 更多K线数据
            ]
        }
        
        logger.info("🔍 原始API响应结构:")
        logger.info(f"  响应码: {raw_response['code']}")
        logger.info(f"  消息: {raw_response['msg']}")
        logger.info(f"  数据条数: {len(raw_response['data'])}")
        logger.info(f"  单条数据示例: {json.dumps(raw_response['data'][0], indent=2)}")
        
        return raw_response
    
    def demonstrate_processed_dataframe(self):
        """演示处理后的DataFrame格式"""
        logger.info("\n📊 处理后的DataFrame格式演示")
        logger.info("=" * 60)
        
        # 创建示例数据
        dates = pd.date_range('2024-01-01 00:00:00', periods=5, freq='H')
        
        # 基础K线数据（处理后）
        df = pd.DataFrame({
            'open': [42150.5, 42280.8, 42350.2, 42420.1, 42380.5],
            'high': [42380.2, 42450.3, 42480.7, 42520.3, 42450.8],
            'low': [42050.1, 42180.5, 42320.1, 42380.2, 42320.7],
            'close': [42280.8, 42350.2, 42420.1, 42380.5, 42410.3],
            'volume_usd': [125680000.50, 98750000.25, 156780000.75, 89650000.30, 45230000.15]
        }, index=dates)
        
        # 添加质量分析字段（我们的改进）
        df['is_complete'] = [True, True, True, True, False]  # 最后一根未完成
        df['completion_percentage'] = [100.0, 100.0, 100.0, 100.0, 65.5]
        df['time_remaining'] = [0, 0, 0, 0, 1245]  # 秒
        df['volume_anomaly'] = [False, False, False, False, True]  # 最后一根成交量异常
        df['volume_level'] = ['normal', 'normal', 'high', 'normal', 'low']
        df['volume_strength'] = [1.27, 1.0, 1.59, 0.91, 0.46]
        df['quality_score'] = [1.0, 1.0, 1.0, 1.0, 0.56]  # 质量评分
        df['overall_quality'] = 'medium'  # 整体质量
        
        logger.info("📋 DataFrame基本信息:")
        logger.info(f"  索引类型: {type(df.index)} (时间索引)")
        logger.info(f"  数据行数: {len(df)}")
        logger.info(f"  数据列数: {len(df.columns)}")
        logger.info(f"  时间范围: {df.index[0]} 到 {df.index[-1]}")
        
        logger.info("\n📊 数据列详解:")
        for col in df.columns:
            dtype = df[col].dtype
            sample_value = df[col].iloc[0]
            logger.info(f"  {col:20} | {str(dtype):10} | 示例: {sample_value}")
        
        logger.info(f"\n📈 最新K线数据:")
        latest = df.iloc[-1]
        logger.info(f"  时间: {df.index[-1]}")
        logger.info(f"  OHLC: {latest['open']:.1f} / {latest['high']:.1f} / {latest['low']:.1f} / {latest['close']:.1f}")
        logger.info(f"  成交量: ${latest['volume_usd']:,.0f}")
        logger.info(f"  完整性: {'✅' if latest['is_complete'] else '❌'} ({latest['completion_percentage']:.1f}%)")
        logger.info(f"  质量评分: {latest['quality_score']:.2f}")
        
        return df
    
    def demonstrate_data_flow(self):
        """演示数据流转过程"""
        logger.info("\n🔄 数据流转过程演示")
        logger.info("=" * 60)
        
        flow_steps = [
            {
                "step": 1,
                "name": "API请求",
                "description": "向CoinGlass API发送请求",
                "input": "symbol='BTCUSDT', interval='1h', limit=200",
                "output": "原始JSON响应"
            },
            {
                "step": 2,
                "name": "数据解析",
                "description": "解析JSON并转换为DataFrame",
                "input": "JSON数据",
                "output": "基础DataFrame (time, open, high, low, close, volume_usd)"
            },
            {
                "step": 3,
                "name": "数据类型转换",
                "description": "转换数据类型和时间索引",
                "input": "字符串格式的价格和时间戳",
                "output": "数值类型的价格和datetime索引"
            },
            {
                "step": 4,
                "name": "完整性分析",
                "description": "分析K线完整性",
                "input": "基础DataFrame",
                "output": "添加is_complete, completion_percentage等字段"
            },
            {
                "step": 5,
                "name": "成交量分析",
                "description": "分析成交量异常",
                "input": "带完整性信息的DataFrame",
                "output": "添加volume_anomaly, volume_level等字段"
            },
            {
                "step": 6,
                "name": "质量评分",
                "description": "计算数据质量评分",
                "input": "完整分析的DataFrame",
                "output": "添加quality_score, overall_quality字段"
            },
            {
                "step": 7,
                "name": "AI分析",
                "description": "传递给AI进行市场分析",
                "input": "完整的DataFrame",
                "output": "AI分析报告"
            }
        ]
        
        for step in flow_steps:
            logger.info(f"步骤{step['step']}: {step['name']}")
            logger.info(f"  描述: {step['description']}")
            logger.info(f"  输入: {step['input']}")
            logger.info(f"  输出: {step['output']}")
            logger.info("")
    
    def demonstrate_ai_usage(self):
        """演示AI模块如何使用K线数据"""
        logger.info("🤖 AI模块数据使用演示")
        logger.info("=" * 60)
        
        # 创建示例数据
        df = self.demonstrate_processed_dataframe()
        
        logger.info("\n📝 AI提示词中的K线数据格式:")
        
        # 模拟AI提示词中的K线摘要
        current_price = df['close'].iloc[-1]
        prev_price = df['close'].iloc[-2]
        price_change = current_price - prev_price
        price_change_percent = (price_change / prev_price * 100)
        
        period_high = df['high'].max()
        period_low = df['low'].min()
        price_position = ((current_price - period_low) / (period_high - period_low) * 100)
        
        current_volume = df['volume_usd'].iloc[-1]
        avg_volume = df['volume_usd'].mean()
        volume_ratio = current_volume / avg_volume
        
        # 数据质量信息
        incomplete_count = len(df[~df['is_complete']])
        anomaly_count = len(df[df['volume_anomaly']])
        
        ai_summary = f"""📊 K线数据分析 (1h周期，{len(df)}根K线)：
  当前价格: ${current_price:,.1f} (${price_change:+.1f}, {price_change_percent:+.2f}%)
  价格区间: ${period_low:,.1f} - ${period_high:,.1f}
  价格位置: {price_position:.1f}% (在周期区间内的位置)
  当前成交量: ${current_volume:,.0f} (平均值的 {volume_ratio:.2f}倍)
  数据时间: {df.index[0].strftime('%m-%d %H:%M')} 至 {df.index[-1].strftime('%m-%d %H:%M')}
  数据质量提醒: ⚠️ 未完成K线: {incomplete_count}根, 异常成交量: {anomaly_count}根"""
        
        logger.info(ai_summary)
        
        logger.info("\n🎯 AI分析重点关注的数据:")
        logger.info("  1. 价格趋势: 基于OHLC数据判断趋势方向")
        logger.info("  2. 成交量确认: 结合成交量验证价格走势")
        logger.info("  3. 数据质量: 根据完整性调整分析可靠性")
        logger.info("  4. 时间因素: 考虑K线周期和时间范围")
        logger.info("  5. 异常检测: 识别数据异常并相应调整策略")
    
    def demonstrate_data_quality_impact(self):
        """演示数据质量对AI分析的影响"""
        logger.info("\n⚖️ 数据质量对AI分析的影响")
        logger.info("=" * 60)
        
        scenarios = [
            {
                "name": "高质量数据",
                "is_complete": True,
                "volume_normal": True,
                "quality_score": 1.0,
                "ai_confidence": "高",
                "recommendation": "可以进行正常的技术分析和交易决策"
            },
            {
                "name": "未完成K线",
                "is_complete": False,
                "volume_normal": True,
                "quality_score": 0.8,
                "ai_confidence": "中等",
                "recommendation": "主要基于已完成K线分析，对最新数据保持谨慎"
            },
            {
                "name": "异常成交量",
                "is_complete": True,
                "volume_normal": False,
                "quality_score": 0.7,
                "ai_confidence": "中等",
                "recommendation": "注意流动性风险，价格可能出现异常波动"
            },
            {
                "name": "混合问题",
                "is_complete": False,
                "volume_normal": False,
                "quality_score": 0.5,
                "ai_confidence": "低",
                "recommendation": "建议等待更多数据确认，降低操作仓位"
            }
        ]
        
        for scenario in scenarios:
            logger.info(f"\n📊 场景: {scenario['name']}")
            logger.info(f"  K线完整: {'✅' if scenario['is_complete'] else '❌'}")
            logger.info(f"  成交量正常: {'✅' if scenario['volume_normal'] else '❌'}")
            logger.info(f"  质量评分: {scenario['quality_score']:.1f}")
            logger.info(f"  AI信心度: {scenario['ai_confidence']}")
            logger.info(f"  建议: {scenario['recommendation']}")
    
    def get_data_structure_summary(self):
        """获取数据结构总结"""
        logger.info("\n📋 K线数据结构总结")
        logger.info("=" * 60)
        
        structure_info = {
            "数据源": "CoinGlass API (futures/spot)",
            "更新频率": "实时",
            "数据格式": "pandas.DataFrame",
            "索引类型": "DatetimeIndex (UTC时间)",
            "基础字段": ["open", "high", "low", "close", "volume_usd"],
            "质量字段": [
                "is_complete", "completion_percentage", "time_remaining",
                "volume_anomaly", "volume_level", "volume_strength",
                "quality_score", "overall_quality"
            ],
            "数据精度": "价格保持原始精度，成交量美元计价",
            "时间范围": "默认200根K线，可配置",
            "支持周期": ["1m", "3m", "5m", "15m", "30m", "1h", "4h", "6h", "8h", "12h", "1d", "1w"]
        }
        
        for key, value in structure_info.items():
            if isinstance(value, list):
                logger.info(f"  {key}: {', '.join(value)}")
            else:
                logger.info(f"  {key}: {value}")


def main():
    """主演示函数"""
    logger.info("🎯 AI模块K线数据结构详解")
    logger.info("=" * 80)
    
    guide = KlineDataStructureGuide()
    
    # 1. 原始API响应
    guide.demonstrate_raw_api_response()
    
    # 2. 处理后的DataFrame
    guide.demonstrate_processed_dataframe()
    
    # 3. 数据流转过程
    guide.demonstrate_data_flow()
    
    # 4. AI使用方式
    guide.demonstrate_ai_usage()
    
    # 5. 数据质量影响
    guide.demonstrate_data_quality_impact()
    
    # 6. 结构总结
    guide.get_data_structure_summary()
    
    logger.info("\n✅ K线数据结构详解完成")
    logger.info("=" * 80)


if __name__ == "__main__":
    main()
