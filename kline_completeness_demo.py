#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线完整性判定演示
详细解释如何判定未完成K线的逻辑
"""

from datetime import datetime, timedelta
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class KlineCompletenessDemo:
    """K线完整性判定演示类"""
    
    def __init__(self):
        self.interval_map = {
            '1m': 60,      # 1分钟 = 60秒
            '3m': 180,     # 3分钟 = 180秒
            '5m': 300,     # 5分钟 = 300秒
            '15m': 900,    # 15分钟 = 900秒
            '30m': 1800,   # 30分钟 = 1800秒
            '1h': 3600,    # 1小时 = 3600秒
            '4h': 14400,   # 4小时 = 14400秒
            '6h': 21600,   # 6小时 = 21600秒
            '8h': 28800,   # 8小时 = 28800秒
            '12h': 43200,  # 12小时 = 43200秒
            '1d': 86400,   # 1天 = 86400秒
            '1w': 604800   # 1周 = 604800秒
        }
    
    def demonstrate_kline_completeness(self, interval: str = "1h"):
        """演示K线完整性判定过程"""
        logger.info(f"🔍 演示 {interval} K线完整性判定过程")
        logger.info("=" * 60)
        
        # 1. 获取当前时间
        current_time = datetime.now()
        logger.info(f"📅 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 2. 模拟最后一根K线的开始时间
        interval_seconds = self.interval_map.get(interval, 3600)
        logger.info(f"⏱️ K线周期: {interval} ({interval_seconds}秒)")
        
        # 模拟不同的K线开始时间场景
        scenarios = [
            ("刚开始的K线", current_time - timedelta(seconds=60)),      # 1分钟前开始
            ("进行中的K线", current_time - timedelta(seconds=1800)),    # 30分钟前开始
            ("即将完成的K线", current_time - timedelta(seconds=3540)),   # 59分钟前开始
            ("已完成的K线", current_time - timedelta(seconds=3660)),    # 61分钟前开始
        ]
        
        for scenario_name, kline_start_time in scenarios:
            logger.info(f"\n📊 场景: {scenario_name}")
            self._analyze_single_kline(kline_start_time, interval_seconds, current_time)
    
    def _analyze_single_kline(self, kline_start_time: datetime, interval_seconds: int, current_time: datetime):
        """分析单根K线的完整性"""
        
        # 计算K线应该结束的时间
        expected_end_time = kline_start_time + timedelta(seconds=interval_seconds)
        
        # 计算已经过去的时间
        elapsed_time = current_time - kline_start_time
        elapsed_seconds = elapsed_time.total_seconds()
        
        # 计算完成度
        completion_percentage = (elapsed_seconds / interval_seconds) * 100
        completion_percentage = min(completion_percentage, 100)  # 最大100%
        
        # 判断是否完整
        is_complete = current_time >= expected_end_time
        
        # 输出分析结果
        logger.info(f"  K线开始时间: {kline_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"  预期结束时间: {expected_end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"  已经过时间: {elapsed_seconds:.0f}秒 ({elapsed_seconds/60:.1f}分钟)")
        logger.info(f"  完成度: {completion_percentage:.1f}%")
        logger.info(f"  是否完整: {'✅ 是' if is_complete else '❌ 否'}")
        
        # 根据完成度给出状态描述
        if completion_percentage < 10:
            status = "🟢 刚开始"
        elif completion_percentage < 50:
            status = "🟡 进行中"
        elif completion_percentage < 90:
            status = "🟠 接近完成"
        elif completion_percentage < 100:
            status = "🔴 即将完成"
        else:
            status = "✅ 已完成"
        
        logger.info(f"  状态: {status}")
        
        return is_complete, completion_percentage
    
    def demonstrate_real_time_scenarios(self):
        """演示实时场景中的K线状态"""
        logger.info("\n🚀 实时场景演示")
        logger.info("=" * 60)
        
        current_time = datetime.now()
        
        # 场景1: 1小时K线
        logger.info("\n📈 1小时K线场景:")
        hour_start = current_time.replace(minute=0, second=0, microsecond=0)
        self._analyze_single_kline(hour_start, 3600, current_time)
        
        # 场景2: 4小时K线
        logger.info("\n📈 4小时K线场景:")
        # 4小时K线通常在 0:00, 4:00, 8:00, 12:00, 16:00, 20:00 开始
        hour_4_starts = [0, 4, 8, 12, 16, 20]
        current_hour = current_time.hour
        
        # 找到当前4小时周期的开始时间
        for start_hour in reversed(hour_4_starts):
            if current_hour >= start_hour:
                hour_4_start = current_time.replace(hour=start_hour, minute=0, second=0, microsecond=0)
                break
        else:
            # 如果当前时间在0点之前，使用前一天的20点
            hour_4_start = (current_time - timedelta(days=1)).replace(hour=20, minute=0, second=0, microsecond=0)
        
        self._analyze_single_kline(hour_4_start, 14400, current_time)
        
        # 场景3: 1天K线
        logger.info("\n📈 1天K线场景:")
        day_start = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
        self._analyze_single_kline(day_start, 86400, current_time)
    
    def demonstrate_edge_cases(self):
        """演示边界情况"""
        logger.info("\n⚠️ 边界情况演示")
        logger.info("=" * 60)
        
        current_time = datetime.now()
        
        # 边界情况1: 刚好到达结束时间
        logger.info("\n🎯 边界情况1: 刚好到达结束时间")
        exact_end_time = current_time - timedelta(seconds=3600)  # 1小时前开始
        self._analyze_single_kline(exact_end_time, 3600, current_time)
        
        # 边界情况2: 超过结束时间1秒
        logger.info("\n🎯 边界情况2: 超过结束时间1秒")
        over_end_time = current_time - timedelta(seconds=3601)  # 1小时1秒前开始
        self._analyze_single_kline(over_end_time, 3600, current_time)
        
        # 边界情况3: 差1秒到结束时间
        logger.info("\n🎯 边界情况3: 差1秒到结束时间")
        before_end_time = current_time - timedelta(seconds=3599)  # 59分59秒前开始
        self._analyze_single_kline(before_end_time, 3600, current_time)
    
    def demonstrate_volume_correlation(self):
        """演示成交量与K线完整性的关系"""
        logger.info("\n💰 成交量与K线完整性关系演示")
        logger.info("=" * 60)
        
        current_time = datetime.now()
        
        # 模拟不同完成度的K线及其成交量特征
        scenarios = [
            (10, "刚开始", "成交量通常较低，市场刚开始活跃"),
            (30, "进行中", "成交量逐渐增加，价格波动加大"),
            (70, "大部分完成", "成交量可能达到峰值，趋势明确"),
            (95, "即将完成", "成交量可能减少，等待下一周期"),
            (100, "已完成", "成交量固定，不再变化")
        ]
        
        for completion, stage, volume_desc in scenarios:
            logger.info(f"\n📊 完成度 {completion}% - {stage}:")
            logger.info(f"  成交量特征: {volume_desc}")
            
            if completion < 100:
                logger.info("  ⚠️ 注意: 数据仍在变化中，分析需谨慎")
            else:
                logger.info("  ✅ 数据稳定，可以进行可靠分析")
    
    def get_practical_recommendations(self):
        """获取实用建议"""
        logger.info("\n💡 实用建议")
        logger.info("=" * 60)
        
        recommendations = [
            "1. 🕐 时间判定: 比较当前时间与K线预期结束时间",
            "2. 📊 数据标记: 为每根K线添加 is_complete 标记",
            "3. ⚠️ 风险提醒: 未完成K线的数据可能发生变化",
            "4. 🎯 分析策略: 主要基于已完成的K线进行分析",
            "5. 💰 成交量考虑: 未完成K线的成交量通常偏低",
            "6. 🔄 实时更新: 定期检查最新K线的完整性状态",
            "7. 📈 趋势确认: 等待K线完成后再确认重要信号",
            "8. 🛡️ 风险控制: 降低对未完成K线数据的依赖程度"
        ]
        
        for rec in recommendations:
            logger.info(f"  {rec}")


def main():
    """主演示函数"""
    logger.info("🎯 K线完整性判定详解演示")
    logger.info("=" * 80)
    
    demo = KlineCompletenessDemo()
    
    # 1. 基础演示
    demo.demonstrate_kline_completeness("1h")
    
    # 2. 实时场景
    demo.demonstrate_real_time_scenarios()
    
    # 3. 边界情况
    demo.demonstrate_edge_cases()
    
    # 4. 成交量关系
    demo.demonstrate_volume_correlation()
    
    # 5. 实用建议
    demo.get_practical_recommendations()
    
    logger.info("\n✅ 演示完成")
    logger.info("=" * 80)


if __name__ == "__main__":
    main()
