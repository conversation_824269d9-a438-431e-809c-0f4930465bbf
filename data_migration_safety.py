#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移安全工具
确保从Windows到Linux的数据迁移过程中不会丢失任何用户数据
"""

import os
import json
import shutil
import hashlib
import platform
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataMigrationSafety:
    """数据迁移安全管理器"""
    
    def __init__(self, source_data_dir: str = "data", backup_dir: str = "migration_backup"):
        self.source_data_dir = Path(source_data_dir)
        self.backup_dir = Path(backup_dir)
        self.migration_log = []
        
        # 创建备份目录
        self.backup_dir.mkdir(exist_ok=True)
        
        # 关键数据文件列表
        self.critical_files = [
            "user_data.json",
            "orders.json", 
            "transaction_log.json",
            "points_history.json",
            "balance_history.json",
            "invitation_system.json",
            "withdrawal_requests.json",
            "unified_users.json"
        ]
        
        logger.info(f"数据迁移安全管理器初始化完成")
        logger.info(f"源数据目录: {self.source_data_dir}")
        logger.info(f"备份目录: {self.backup_dir}")
    
    def log_operation(self, operation: str, status: str, details: str = ""):
        """记录操作日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "status": status,
            "details": details
        }
        self.migration_log.append(log_entry)
        
        if status == "SUCCESS":
            logger.info(f"✅ {operation}: {details}")
        elif status == "WARNING":
            logger.warning(f"⚠️ {operation}: {details}")
        else:
            logger.error(f"❌ {operation}: {details}")
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """计算文件的SHA256哈希值"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return ""
    
    def validate_json_file(self, file_path: Path) -> Tuple[bool, Optional[Dict]]:
        """验证JSON文件的有效性"""
        try:
            if not file_path.exists():
                return False, None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return True, data
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误 {file_path}: {e}")
            return False, None
        except Exception as e:
            logger.error(f"文件读取错误 {file_path}: {e}")
            return False, None
    
    def create_comprehensive_backup(self) -> bool:
        """创建全面的数据备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_subdir = self.backup_dir / f"backup_{timestamp}"
            backup_subdir.mkdir(exist_ok=True)
            
            self.log_operation("创建备份目录", "SUCCESS", str(backup_subdir))
            
            # 备份所有数据文件
            if not self.source_data_dir.exists():
                self.log_operation("源数据目录检查", "WARNING", "源数据目录不存在，可能是新部署")
                return True
            
            backup_manifest = {
                "backup_time": datetime.now().isoformat(),
                "source_system": platform.system(),
                "files": {}
            }
            
            # 复制所有文件
            for item in self.source_data_dir.rglob("*"):
                if item.is_file():
                    relative_path = item.relative_to(self.source_data_dir)
                    backup_file = backup_subdir / relative_path
                    
                    # 确保目标目录存在
                    backup_file.parent.mkdir(parents=True, exist_ok=True)
                    
                    # 复制文件
                    shutil.copy2(item, backup_file)
                    
                    # 计算哈希值
                    original_hash = self.calculate_file_hash(item)
                    backup_hash = self.calculate_file_hash(backup_file)
                    
                    if original_hash != backup_hash:
                        self.log_operation("文件备份验证", "ERROR", f"哈希不匹配: {relative_path}")
                        return False
                    
                    backup_manifest["files"][str(relative_path)] = {
                        "size": item.stat().st_size,
                        "hash": original_hash,
                        "backup_hash": backup_hash
                    }
                    
                    self.log_operation("文件备份", "SUCCESS", str(relative_path))
            
            # 保存备份清单
            manifest_file = backup_subdir / "backup_manifest.json"
            with open(manifest_file, 'w', encoding='utf-8') as f:
                json.dump(backup_manifest, f, ensure_ascii=False, indent=2)
            
            self.log_operation("备份清单保存", "SUCCESS", str(manifest_file))
            
            # 验证关键文件
            for critical_file in self.critical_files:
                original_file = self.source_data_dir / critical_file
                backup_file = backup_subdir / critical_file
                
                if original_file.exists():
                    if not backup_file.exists():
                        self.log_operation("关键文件备份", "ERROR", f"备份文件不存在: {critical_file}")
                        return False
                    
                    # 验证JSON格式
                    is_valid, _ = self.validate_json_file(backup_file)
                    if not is_valid:
                        self.log_operation("关键文件验证", "ERROR", f"备份文件JSON无效: {critical_file}")
                        return False
                    
                    self.log_operation("关键文件验证", "SUCCESS", critical_file)
            
            self.log_operation("全面备份", "SUCCESS", f"备份完成，共 {len(backup_manifest['files'])} 个文件")
            return True
            
        except Exception as e:
            self.log_operation("全面备份", "ERROR", f"备份异常: {e}")
            return False
    
    def validate_user_data_integrity(self) -> bool:
        """验证用户数据完整性"""
        try:
            user_data_file = self.source_data_dir / "user_data.json"
            
            if not user_data_file.exists():
                self.log_operation("用户数据验证", "WARNING", "用户数据文件不存在")
                return True
            
            is_valid, user_data = self.validate_json_file(user_data_file)
            if not is_valid:
                self.log_operation("用户数据验证", "ERROR", "用户数据JSON格式无效")
                return False
            
            if not isinstance(user_data, dict):
                self.log_operation("用户数据验证", "ERROR", f"用户数据类型错误: {type(user_data)}")
                return False
            
            if len(user_data) == 0:
                self.log_operation("用户数据验证", "WARNING", "用户数据为空")
                return True
            
            # 验证用户数据结构
            valid_users = 0
            total_users = len(user_data)
            
            for user_id, data in user_data.items():
                if self._validate_single_user_data(user_id, data):
                    valid_users += 1
            
            if valid_users < total_users * 0.8:  # 至少80%的用户数据有效
                self.log_operation(
                    "用户数据验证", 
                    "ERROR", 
                    f"有效用户比例过低: {valid_users}/{total_users}"
                )
                return False
            
            self.log_operation(
                "用户数据验证", 
                "SUCCESS", 
                f"验证通过: {valid_users}/{total_users} 个用户"
            )
            return True
            
        except Exception as e:
            self.log_operation("用户数据验证", "ERROR", f"验证异常: {e}")
            return False
    
    def _validate_single_user_data(self, user_id: str, user_data: Any) -> bool:
        """验证单个用户数据"""
        try:
            # 检查用户ID
            int(user_id)
            
            # 检查数据类型
            if not isinstance(user_data, dict):
                return False
            
            # 检查必需字段
            required_fields = ["user_id", "points", "total_recharged", "register_time"]
            for field in required_fields:
                if field not in user_data:
                    return False
            
            # 检查数值字段
            if not isinstance(user_data["points"], (int, float)) or user_data["points"] < 0:
                return False
            
            if not isinstance(user_data["total_recharged"], (int, float)) or user_data["total_recharged"] < 0:
                return False
            
            return True
            
        except Exception:
            return False
    
    def prepare_for_linux_migration(self) -> bool:
        """为Linux迁移做准备"""
        try:
            # 1. 创建全面备份
            if not self.create_comprehensive_backup():
                return False
            
            # 2. 验证用户数据完整性
            if not self.validate_user_data_integrity():
                return False
            
            # 3. 检查文件权限兼容性
            self._check_file_permissions()
            
            # 4. 生成迁移报告
            self._generate_migration_report()
            
            self.log_operation("Linux迁移准备", "SUCCESS", "所有检查通过")
            return True
            
        except Exception as e:
            self.log_operation("Linux迁移准备", "ERROR", f"准备异常: {e}")
            return False
    
    def _check_file_permissions(self):
        """检查文件权限兼容性"""
        try:
            if not self.source_data_dir.exists():
                return
            
            for file_path in self.source_data_dir.rglob("*.json"):
                try:
                    # 检查文件是否可读写
                    with open(file_path, 'r', encoding='utf-8') as f:
                        f.read(1)  # 尝试读取
                    
                    # 检查是否可写（通过追加模式测试）
                    with open(file_path, 'a', encoding='utf-8') as f:
                        pass
                    
                    self.log_operation("文件权限检查", "SUCCESS", str(file_path.name))
                    
                except PermissionError:
                    self.log_operation("文件权限检查", "WARNING", f"权限问题: {file_path}")
                except Exception as e:
                    self.log_operation("文件权限检查", "WARNING", f"检查异常 {file_path}: {e}")
                    
        except Exception as e:
            self.log_operation("文件权限检查", "ERROR", f"权限检查异常: {e}")
    
    def _generate_migration_report(self):
        """生成迁移报告"""
        try:
            report = {
                "migration_time": datetime.now().isoformat(),
                "source_system": platform.system(),
                "target_system": "Linux",
                "operations": self.migration_log,
                "summary": {
                    "total_operations": len(self.migration_log),
                    "successful_operations": len([op for op in self.migration_log if op["status"] == "SUCCESS"]),
                    "warnings": len([op for op in self.migration_log if op["status"] == "WARNING"]),
                    "errors": len([op for op in self.migration_log if op["status"] == "ERROR"])
                }
            }
            
            report_file = self.backup_dir / f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.log_operation("迁移报告生成", "SUCCESS", str(report_file))
            
        except Exception as e:
            self.log_operation("迁移报告生成", "ERROR", f"报告生成异常: {e}")
    
    def verify_migration_success(self, target_data_dir: str = "/opt/crypto_trading_bot/data") -> bool:
        """验证迁移成功"""
        try:
            target_path = Path(target_data_dir)
            
            if not target_path.exists():
                self.log_operation("迁移验证", "ERROR", "目标数据目录不存在")
                return False
            
            # 验证关键文件存在
            for critical_file in self.critical_files:
                source_file = self.source_data_dir / critical_file
                target_file = target_path / critical_file
                
                if source_file.exists():
                    if not target_file.exists():
                        self.log_operation("迁移验证", "ERROR", f"目标文件不存在: {critical_file}")
                        return False
                    
                    # 验证文件内容
                    source_hash = self.calculate_file_hash(source_file)
                    target_hash = self.calculate_file_hash(target_file)
                    
                    if source_hash != target_hash:
                        self.log_operation("迁移验证", "ERROR", f"文件哈希不匹配: {critical_file}")
                        return False
                    
                    self.log_operation("迁移验证", "SUCCESS", f"文件验证通过: {critical_file}")
            
            # 验证用户数据
            target_user_file = target_path / "user_data.json"
            if target_user_file.exists():
                is_valid, user_data = self.validate_json_file(target_user_file)
                if not is_valid:
                    self.log_operation("迁移验证", "ERROR", "目标用户数据JSON无效")
                    return False
                
                if isinstance(user_data, dict) and len(user_data) > 0:
                    self.log_operation("迁移验证", "SUCCESS", f"用户数据验证通过: {len(user_data)} 个用户")
                else:
                    self.log_operation("迁移验证", "WARNING", "目标用户数据为空")
            
            self.log_operation("迁移验证", "SUCCESS", "所有验证通过")
            return True
            
        except Exception as e:
            self.log_operation("迁移验证", "ERROR", f"验证异常: {e}")
            return False

def main():
    """主函数"""
    print("=" * 60)
    print("数据迁移安全工具")
    print("=" * 60)
    
    migrator = DataMigrationSafety()
    
    print("\n🔍 开始数据迁移安全检查...")
    
    # 执行迁移准备
    if migrator.prepare_for_linux_migration():
        print("\n✅ 数据迁移准备完成！")
        print(f"📁 备份目录: {migrator.backup_dir}")
        print("\n下一步:")
        print("1. 将项目文件上传到Linux服务器")
        print("2. 运行 deploy_linux.sh 脚本")
        print("3. 使用此工具验证迁移结果")
        return 0
    else:
        print("\n❌ 数据迁移准备失败！")
        print("请检查错误信息并修复问题后重试。")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
