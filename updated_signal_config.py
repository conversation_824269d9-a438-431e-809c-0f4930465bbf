#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新后的信号规则配置
根据用户要求修改的新警报规则
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any

# 新的信号规则配置
SIGNAL_RULES = {
    "rsi_signals": {
        "name": "RSI信号",
        "description": "使用CoinGlass RSI数据",
        "rules": {
            "overbought_threshold": 80,  # 超买阈值从70提升到80
            "oversold_threshold": 20,    # 超卖阈值从30降低到20
            "cooldown_period": 1800,     # 30分钟冷却期
            "monitored_periods": ["1h", "4h"]  # 监控周期
        },
        "data_source": "CoinGlass API",
        "status": "active"
    },
    
    "funding_rate_signals": {
        "name": "资金费率信号",
        "description": "使用币安资金费率数据",
        "rules": {
            "high_threshold": 1.0,       # >=1%
            "low_threshold": -1.0,       # <=-1%
            "cooldown_period": 1800,     # 30分钟冷却期
            "data_source": "Binance API"
        },
        "data_source": "Binance API",
        "status": "active"
    },
    
    "open_interest_signals": {
        "name": "持仓量信号",
        "description": "使用CoinGlass持仓量数据",
        "rules": {
            "change_threshold": 5.0,     # 15分钟变化>5%
            "time_period": "15m",        # 15分钟周期
            "monitoring_period": "15m",  # 监控周期
            "cooldown_period": 1800,     # 30分钟冷却期
            "cache_directory": "data/coinglass"
        },
        "data_source": "CoinGlass API",
        "status": "active"
    },
    
    "transfer_alerts": {
        "name": "大额转账警报",
        "description": "大额转账监控",
        "rules": {
            "btc_threshold": 1000,
            "eth_threshold": 10000,
            "usdt_threshold": 50000000
        },
        "data_source": "Whale Alert",
        "status": "disabled"  # 已禁用
    }
}

# 信号订阅类型映射
SIGNAL_SUBSCRIPTION_MAPPING = {
    "rsi": ["rsi", "ai_technical_analysis", "ai_signals", "ai_monitor"],
    "funding_rate": ["funding_rate", "ai_activity", "ai_monitor"],
    "open_interest": ["open_interest", "ai_anomaly", "ai_monitor"]
    # 大额转账已从映射中移除
}

# CoinGlass API配置
COINGLASS_CONFIG = {
    "api_key": "c40fbbee201d4dfab3a4b62f37f0b610",
    "base_url": "https://open-api-v4.coinglass.com",
    "endpoints": {
        "futures_markets": "/api/futures/coins-markets",
        "rsi_data": "/api/futures/rsi/list"
    },
    "rate_limit": {
        "requests_per_minute": 60,
        "delay_between_requests": 0.1
    }
}

# 币安API配置
BINANCE_CONFIG = {
    "base_url": "https://fapi.binance.com",
    "endpoints": {
        "funding_rate": "/fapi/v1/premiumIndex",
        "exchange_info": "/fapi/v1/exchangeInfo"
    }
}

def save_updated_config():
    """保存更新后的配置"""
    config_data = {
        "update_time": datetime.now().isoformat(),
        "version": "2.0",
        "signal_rules": SIGNAL_RULES,
        "subscription_mapping": SIGNAL_SUBSCRIPTION_MAPPING,
        "api_config": {
            "coinglass": COINGLASS_CONFIG,
            "binance": BINANCE_CONFIG
        }
    }
    
    # 确保目录存在
    os.makedirs("data", exist_ok=True)
    
    # 保存配置
    config_file = "data/updated_signal_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 更新后的信号配置已保存到: {config_file}")
    return config_file

def validate_config():
    """验证配置的有效性"""
    print("🔍 验证信号配置...")
    
    # 检查RSI阈值
    rsi_config = SIGNAL_RULES["rsi_signals"]["rules"]
    if rsi_config["overbought_threshold"] != 80:
        print("❌ RSI超买阈值不正确")
        return False
    if rsi_config["oversold_threshold"] != 20:
        print("❌ RSI超卖阈值不正确")
        return False
    
    # 检查资金费率阈值
    funding_config = SIGNAL_RULES["funding_rate_signals"]["rules"]
    if funding_config["high_threshold"] != 1.0:
        print("❌ 资金费率高阈值不正确")
        return False
    if funding_config["low_threshold"] != -1.0:
        print("❌ 资金费率低阈值不正确")
        return False
    
    # 检查持仓量阈值
    oi_config = SIGNAL_RULES["open_interest_signals"]["rules"]
    if oi_config["change_threshold"] != 5.0:
        print("❌ 持仓量变化阈值不正确")
        return False
    
    # 检查大额转账是否已禁用
    transfer_config = SIGNAL_RULES["transfer_alerts"]
    if transfer_config["status"] != "disabled":
        print("❌ 大额转账功能未正确禁用")
        return False
    
    print("✅ 所有配置验证通过")
    return True

def print_config_summary():
    """打印配置摘要"""
    print("\n📋 更新后的信号规则摘要:")
    print("=" * 60)
    
    for signal_type, config in SIGNAL_RULES.items():
        status_emoji = "✅" if config["status"] == "active" else "🚫"
        print(f"{status_emoji} {config['name']}: {config['status']}")
        
        if config["status"] == "active":
            rules = config["rules"]
            if signal_type == "rsi_signals":
                print(f"   - 超买阈值: >{rules['overbought_threshold']}")
                print(f"   - 超卖阈值: <{rules['oversold_threshold']}")
                print(f"   - 冷却期: {rules['cooldown_period']}秒")
            elif signal_type == "funding_rate_signals":
                print(f"   - 高费率阈值: ≥{rules['high_threshold']}%")
                print(f"   - 低费率阈值: ≤{rules['low_threshold']}%")
                print(f"   - 冷却期: {rules['cooldown_period']}秒")
            elif signal_type == "open_interest_signals":
                print(f"   - 变化阈值: ≥{rules['change_threshold']}%")
                print(f"   - 时间周期: {rules['time_period']}")
                print(f"   - 冷却期: {rules['cooldown_period']}秒")
        
        print(f"   - 数据源: {config['data_source']}")
        print()

def main():
    """主函数"""
    print("🔧 信号规则配置更新工具")
    print("=" * 60)
    
    # 验证配置
    if not validate_config():
        print("❌ 配置验证失败")
        return
    
    # 打印配置摘要
    print_config_summary()
    
    # 保存配置
    config_file = save_updated_config()
    
    print("🎉 信号规则更新完成!")
    print(f"📁 配置文件: {config_file}")
    print("\n📝 主要更改:")
    print("   - RSI信号: 超买>80, 超卖<20, 1小时冷却")
    print("   - 资金费率: ≥1%或≤-1%, 1小时冷却")
    print("   - 持仓量: 15分钟变化>5%, 1小时冷却")
    print("   - 大额转账: 已禁用")

if __name__ == "__main__":
    main() 