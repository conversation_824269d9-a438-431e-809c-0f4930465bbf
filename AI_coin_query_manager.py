#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI币种查询功能 - 核心查询管理器
负责数据获取、缓存管理和AI分析调用的核心模块
"""

import os
import json
import asyncio
import logging
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd

# 导入自定义模块
try:
    from AI_kline_fetcher import AIKlineFetcher
except ImportError:
    AIKlineFetcher = None
    print("⚠️ AI_kline_fetcher 模块未找到")

# 导入核心分析模块
from AI_technical_analysis import AITechnicalAnalysis
from AI_prompt_builder import AIPromptBuilder

# Google Gemini API配置
try:
    import requests
    GEMINI_AVAILABLE = True  # requests库应该总是可用的
    print("✅ requests库可用，AI分析功能启用")
except ImportError as e:
    GEMINI_AVAILABLE = False
    print(f"⚠️ requests库不可用，AI分析功能禁用: {e}")

GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"
# 主要API密钥
GEMINI_API_KEY = "AIzaSyBt4pIYmLYheuMpXSCj5VLkCA-fhfdEVT4"
# 备用API密钥列表（可以添加更多备用密钥）
BACKUP_API_KEYS = [
    "AIzaSyBt4pIYmLYheuMpXSCj5VLkCA-fhfdEVT4",
    "AIzaSyBSllSwrObqvUiXqFG5RUJXB6woZoBSaTk",
    "AIzaSyB1PiY-qfnM8Yj8kctuLhSPW8ckUJYu5-U",
    "AIzaSyB3YP05qliWlJnmL6w07rMRT7BUpl_dM94",
        # 主密钥也放入备用列表
]

logger = logging.getLogger(__name__)

class AICoinQueryManager:
    """AI币种查询管理器 - 整合所有功能的核心类"""
    
    def __init__(self, cache_dir: str = "data/cache/coinglass", 
                 coinglass_data_dir: str = "data/coinglass",
                 gemini_api_key: str = None):
        """初始化币种查询管理器
        
        Args:
            cache_dir: 缓存目录
            coinglass_data_dir: CoinGlass数据目录
            gemini_api_key: Google Gemini API密钥
        """
        self.cache_dir = cache_dir
        self.coinglass_data_dir = coinglass_data_dir
        
        # 初始化子模块 - 增加错误处理
        try:
            self.kline_fetcher = AIKlineFetcher(cache_dir)
            print("✅ K线数据获取器初始化成功")
        except Exception as e:
            print(f"⚠️ K线数据获取器初始化失败: {e}")
            self.kline_fetcher = None
            
        try:
            self.technical_analyzer = AITechnicalAnalysis()
            print("✅ 技术分析器初始化成功")
        except Exception as e:
            print(f"⚠️ 技术分析器初始化失败: {e}")
            self.technical_analyzer = None
            
        try:
            self.prompt_builder = AIPromptBuilder()
            print("✅ 提示构建器初始化成功")
        except Exception as e:
            print(f"⚠️ 提示构建器初始化失败: {e}")
            self.prompt_builder = None
        
        # Google Gemini API配置
        self.gemini_api_key = gemini_api_key or GEMINI_API_KEY
        if GEMINI_AVAILABLE and self.gemini_api_key:
            logger.info("✅ Google Gemini API已配置")
            print("✅ Google Gemini API已配置")
        else:
            logger.warning("⚠️ Google Gemini API未配置，将无法使用AI分析功能")
            print("⚠️ Google Gemini API未配置，将无法使用AI分析功能")
        
        # AI分析缓存
        self._ai_cache = {}
        self._cache_ttl = 300  # 5分钟缓存
        
        # 确保目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
        logger.info("✅ AI币种查询管理器初始化完成")
    
    async def analyze_coin(self, symbol: str, market_type: str = "futures", 
                          interval: str = "1h", use_ai: bool = True) -> Dict[str, Any]:
        """完整的币种分析流程
        
        Args:
            symbol: 币种符号，如 'BTCUSDT'
            market_type: 市场类型 'futures' 或 'spot'
            interval: K线周期
            use_ai: 是否使用AI分析
            
        Returns:
            完整的分析结果字典
        """
        try:
            logger.info(f"🔍 开始分析 {symbol} ({market_type}, {interval})")
            
            analysis_result = {
                'symbol': symbol,
                'market_type': market_type,
                'interval': interval,
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'data': {},
                'error': None
            }
            
            # 步骤1: 获取K线数据
            logger.info("📊 获取K线数据...")
            kline_success, kline_data, kline_msg = await self._fetch_kline_data(symbol, market_type, interval)
            
            if not kline_success or kline_data is None:
                analysis_result['error'] = f"K线数据获取失败: {kline_msg}"
                return analysis_result
            
            analysis_result['data']['kline_summary'] = {
                'data_points': len(kline_data),
                'time_range': f"{kline_data.index[0]} 到 {kline_data.index[-1]}",
                'current_price': float(kline_data['close'].iloc[-1]),
                'message': kline_msg
            }
            
            # 步骤2: 计算技术指标
            logger.info("🔧 计算技术指标...")
            technical_indicators = self.technical_analyzer.calculate_all_indicators(kline_data)
            analysis_result['data']['technical_indicators'] = technical_indicators
            
            # 步骤2.5: 获取基差数据（仅合约市场）并整合到技术指标中
            if market_type == "futures":
                logger.info("📊 获取基差数据并整合到技术指标...")
                try:
                    basis_success, basis_df, basis_msg = self.kline_fetcher.fetch_basis_history(
                        symbol, interval, limit=100
                    )
                    
                    if basis_success and basis_df is not None:
                        # 提取基差基础数据并整合到技术指标中
                        current_basis = float(basis_df['close_basis'].iloc[-1])
                        avg_basis = float(basis_df['close_basis'].mean())
                        basis_change = float(basis_df['close_change'].iloc[-1]) if len(basis_df) >= 1 else 0
                        
                        # 将基差数据添加到技术指标中
                        technical_indicators['current_basis'] = current_basis
                        technical_indicators['avg_basis'] = avg_basis
                        technical_indicators['basis_change'] = basis_change
                        
                        # 基差市场情绪评分
                        if current_basis > 0.1:
                            technical_indicators['basis_sentiment'] = "极度乐观"
                            technical_indicators['basis_sentiment_score'] = 80
                        elif current_basis > 0.05:
                            technical_indicators['basis_sentiment'] = "偏向乐观"
                            technical_indicators['basis_sentiment_score'] = 60
                        elif current_basis > 0:
                            technical_indicators['basis_sentiment'] = "轻微乐观"
                            technical_indicators['basis_sentiment_score'] = 40
                        elif current_basis > -0.05:
                            technical_indicators['basis_sentiment'] = "轻微悲观"
                            technical_indicators['basis_sentiment_score'] = -40
                        else:
                            technical_indicators['basis_sentiment'] = "明显悲观"
                            technical_indicators['basis_sentiment_score'] = -60
                        
                        # 套利机会评估
                        abs_basis = abs(current_basis)
                        if abs_basis > 0.1:
                            technical_indicators['arbitrage_opportunity'] = "高"
                        elif abs_basis > 0.05:
                            technical_indicators['arbitrage_opportunity'] = "中等"
                        else:
                            technical_indicators['arbitrage_opportunity'] = "低"
                        
                        logger.info(f"✅ 基差数据整合完成: {current_basis:.4f}% ({technical_indicators['basis_sentiment']})")
                        
                    else:
                        logger.warning(f"⚠️ 基差数据获取失败: {basis_msg}")
                        # 设置默认值
                        technical_indicators['current_basis'] = 0.0
                        technical_indicators['basis_sentiment'] = "数据不可用"
                        technical_indicators['arbitrage_opportunity'] = "未知"
                        
                except Exception as e:
                    logger.error(f"❌ 基差数据获取异常: {str(e)}")
                    technical_indicators['current_basis'] = 0.0
                    technical_indicators['basis_sentiment'] = "获取失败"
                    technical_indicators['arbitrage_opportunity'] = "未知"
            
            # 步骤3: 生成交易信号
            logger.info("🚦 生成交易信号...")
            trading_signals = self.technical_analyzer.get_trading_signals(technical_indicators)
            analysis_result['data']['trading_signals'] = trading_signals
            
            # 步骤4: 获取市场综合数据
            logger.info("💰 获取市场数据...")
            market_data = self._load_coinglass_market_data()
            analysis_result['data']['market_data_available'] = market_data is not None
            
            # 步骤5: AI分析（如果启用）
            if use_ai and GEMINI_AVAILABLE and self.gemini_api_key:
                logger.info("🤖 执行AI分析...")
                ai_analysis = await self._perform_ai_analysis(
                    symbol, market_type, interval, kline_data, 
                    technical_indicators, market_data, trading_signals
                )
                analysis_result['data']['ai_analysis'] = ai_analysis
            else:
                logger.info("ℹ️ 跳过AI分析")
                analysis_result['data']['ai_analysis'] = {
                    'available': False,
                    'reason': 'AI功能未启用或API未配置'
                }
            
            # 步骤6: 生成结果摘要
            analysis_result['data']['summary'] = self._generate_analysis_summary(
                symbol, kline_data, technical_indicators, trading_signals
            )
            
            analysis_result['success'] = True
            logger.info(f"✅ {symbol} 分析完成")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ 分析 {symbol} 时发生错误: {str(e)}")
            analysis_result['error'] = f"分析过程中发生错误: {str(e)}"
            return analysis_result
    
    async def _fetch_kline_data(self, symbol: str, market_type: str, 
                               interval: str) -> Tuple[bool, Optional[pd.DataFrame], str]:
        """获取K线数据"""
        try:
            if market_type == "futures":
                return self.kline_fetcher.fetch_futures_kline(symbol, interval, limit=200)
            else:
                return self.kline_fetcher.fetch_spot_kline(symbol, interval, limit=200)
        except Exception as e:
            logger.error(f"获取K线数据异常: {str(e)}")
            return False, None, f"获取K线数据异常: {str(e)}"
    
    def _load_coinglass_market_data(self) -> Optional[Dict[str, Any]]:
        """加载CoinGlass市场数据"""
        try:
            # 查找最新的数据目录
            if not os.path.exists(self.coinglass_data_dir):
                logger.warning(f"CoinGlass数据目录不存在: {self.coinglass_data_dir}")
                return None
            
            # 获取最新的缓存目录
            cache_dirs = [d for d in os.listdir(self.coinglass_data_dir) 
                         if os.path.isdir(os.path.join(self.coinglass_data_dir, d))]
            
            if not cache_dirs:
                logger.warning("没有找到CoinGlass缓存目录")
                return None
            
            # 按时间排序，获取最新的
            cache_dirs.sort(reverse=True)
            latest_dir = os.path.join(self.coinglass_data_dir, cache_dirs[0])
            
            market_data = {}
            
            # 读取futures数据
            futures_file = os.path.join(latest_dir, "futures.json")
            if os.path.exists(futures_file):
                with open(futures_file, 'r', encoding='utf-8') as f:
                    market_data['futures'] = json.load(f)
                logger.info(f"加载了 {len(market_data['futures'])} 个合约币种数据")
            
            # 读取spot数据
            spot_file = os.path.join(latest_dir, "spot.json")
            if os.path.exists(spot_file):
                with open(spot_file, 'r', encoding='utf-8') as f:
                    market_data['spot'] = json.load(f)
                logger.info(f"加载了 {len(market_data['spot'])} 个现货币种数据")

            # 读取rsi数据
            rsi_file = os.path.join(latest_dir, "rsi.json")
            if os.path.exists(rsi_file):
                with open(rsi_file, 'r', encoding='utf-8') as f:
                    market_data['rsi'] = json.load(f)
                logger.info(f"加载了 {len(market_data['rsi'])} 个RSI币种数据")

            return market_data if market_data else None
            
        except Exception as e:
            logger.error(f"加载CoinGlass市场数据失败: {str(e)}")
            return None
    
    async def _perform_ai_analysis(self, symbol: str, market_type: str, interval: str,
                                  kline_data: pd.DataFrame, technical_indicators: Dict[str, Any],
                                  market_data: Optional[Dict[str, Any]], 
                                  trading_signals: Dict[str, str]) -> Dict[str, Any]:
        """执行AI分析"""
        try:
            # 检查缓存
            cache_key = f"{symbol}_{market_type}_{interval}_{int(time.time() // self._cache_ttl)}"
            if cache_key in self._ai_cache:
                logger.info(f"🔄 使用缓存的AI分析结果: {symbol}")
                cached_result = self._ai_cache[cache_key].copy()
                cached_result['from_cache'] = True
                return cached_result
            
            # 构建提示词
            prompt = self.prompt_builder.build_comprehensive_analysis_prompt(
                symbol, market_type, interval, kline_data, 
                technical_indicators, market_data or {}, trading_signals
            )
            
            # 调用Google Gemini API
            if GEMINI_AVAILABLE and self.gemini_api_key:
                response = await self._call_gemini_api(prompt)
                result = {
                    'available': True,
                    'analysis': response,
                    'prompt_length': len(prompt),
                    'from_cache': False
                }
                
                # 将结果存入缓存
                self._ai_cache[cache_key] = result.copy()
                
                # 清理过期缓存
                self._cleanup_cache()
                
                return result
            else:
                return {
                    'available': False,
                    'reason': 'Google Gemini API不可用'
                }
                
        except Exception as e:
            logger.error(f"AI分析失败: {str(e)}")
            return {
                'available': False,
                'error': str(e)
            }
    
    def _cleanup_cache(self):
        """清理过期的AI分析缓存"""
        try:
            current_time = int(time.time() // self._cache_ttl)
            keys_to_remove = []
            
            for key in self._ai_cache.keys():
                # 提取时间戳
                parts = key.split('_')
                if len(parts) >= 4:
                    try:
                        cache_time = int(parts[-1])
                        if current_time - cache_time > 1:  # 超过1个缓存周期
                            keys_to_remove.append(key)
                    except ValueError:
                        keys_to_remove.append(key)  # 无效格式的key
            
            for key in keys_to_remove:
                del self._ai_cache[key]
            
            if keys_to_remove:
                logger.info(f"🧹 清理了 {len(keys_to_remove)} 个过期的AI分析缓存")
                
        except Exception as e:
            logger.error(f"清理AI缓存失败: {str(e)}")
    
    async def _call_gemini_api(self, prompt: str) -> str:
        """调用Google Gemini API"""
        try:
            # 构建系统提示词
            system_prompt = "你是一位专业的数字货币分析师，具有丰富的技术分析和市场分析经验。请基于提供的数据进行专业分析。"
            full_prompt = f"{system_prompt}\n\n{prompt}"
            
            # 构建请求数据
            request_data = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": full_prompt
                            }
                        ]
                    }
                ]
            }
            
            # 设置请求头
            headers = {
                'Content-Type': 'application/json',
                'X-goog-api-key': self.gemini_api_key
            }
            
            # 发送异步请求 - 增加超时时间和重试机制
            loop = asyncio.get_event_loop()
            
            # 尝试多次请求，逐步增加超时时间并轮换API密钥
            max_retries = len(BACKUP_API_KEYS) * 2  # 每个密钥重试2次
            timeout_values = [120, 180, 300]  # 大幅增加超时时间：2分钟、3分钟、5分钟
            
            for attempt in range(max_retries):
                try:
                    # 选择API密钥（轮换使用）
                    key_index = attempt // 2  # 每个密钥用2次
                    if key_index < len(BACKUP_API_KEYS):
                        current_api_key = BACKUP_API_KEYS[key_index]
                    else:
                        current_api_key = self.gemini_api_key  # 回到主密钥
                    
                    # 选择超时时间
                    timeout_index = min(attempt % 3, len(timeout_values) - 1)
                    timeout = timeout_values[timeout_index]
                    
                    # 更新请求头中的API密钥
                    current_headers = headers.copy()
                    current_headers['X-goog-api-key'] = current_api_key
                    
                    logger.info(f"尝试AI分析请求 (第{attempt+1}次)，使用密钥索引: {key_index}，超时设置: {timeout}秒")
                    
                    response = await loop.run_in_executor(
                        None, 
                        lambda: requests.post(
                            GEMINI_API_URL, 
                            headers=current_headers, 
                            json=request_data, 
                            timeout=timeout
                        )
                    )
                    
                    # 如果请求成功，跳出重试循环
                    logger.info(f"AI分析请求成功 (第{attempt+1}次尝试)")
                    break
                    
                except requests.exceptions.Timeout:
                    logger.warning(f"AI分析请求超时 (第{attempt+1}次尝试，{timeout}秒)")
                    if attempt == max_retries - 1:
                        # 最后一次尝试失败，抛出异常
                        raise
                    # 等待一段时间后重试
                    await asyncio.sleep(min(2 ** (attempt % 3), 8))  # 指数退避，最大8秒
                    
                except requests.exceptions.HTTPError as e:
                    if e.response.status_code == 429:  # 频率限制
                        logger.warning(f"API频率限制 (第{attempt+1}次尝试)，尝试下一个密钥")
                        if attempt == max_retries - 1:
                            raise
                        await asyncio.sleep(min(2 ** (attempt % 3), 8))
                    else:
                        logger.error(f"AI分析HTTP错误 (第{attempt+1}次尝试): {str(e)}")
                        raise
                        
                except Exception as e:
                    # 其他错误，记录并继续尝试
                    logger.error(f"AI分析请求失败 (第{attempt+1}次尝试): {str(e)}")
                    if attempt == max_retries - 1:
                        raise
                    await asyncio.sleep(1)
            
            if response.status_code == 200:
                result = response.json()
                
                # 解析Gemini API响应
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        if len(candidate['content']['parts']) > 0:
                            return candidate['content']['parts'][0]['text'].strip()
                
                logger.error(f"Gemini API响应格式异常: {result}")
                return "AI分析响应格式异常，请稍后重试"
            else:
                logger.error(f"Gemini API请求失败: {response.status_code} - {response.text}")
                return f"AI分析请求失败: HTTP {response.status_code}"
            
        except requests.exceptions.Timeout as e:
            logger.error(f"Gemini API请求超时: {str(e)}")
            return "🤖 AI分析系统繁忙，请稍后重试\n\n⏰ 当前请求较多，响应时间较长，建议您：\n- 稍等1-2分钟后重新尝试\n- 或查看基础技术分析数据"
        except requests.exceptions.ConnectionError as e:
            logger.error(f"Gemini API连接失败: {str(e)}")
            return "🌐 AI分析网络连接异常\n\n📡 网络连接不稳定，建议您：\n- 检查网络连接\n- 稍后重新尝试分析"
        except Exception as e:
            logger.error(f"Gemini API调用失败: {str(e)}")
            # 根据错误类型提供更友好的消息
            error_str = str(e).lower()
            if "timeout" in error_str:
                return "🤖 AI分析响应超时\n\n⏰ 系统处理时间较长，建议您：\n- 稍等片刻后重试\n- 或先查看技术指标数据"
            elif "connection" in error_str or "network" in error_str:
                return "🌐 AI分析网络异常\n\n📡 网络连接问题，建议您：\n- 检查网络连接\n- 稍后重新尝试"
            elif "quota" in error_str or "limit" in error_str:
                return "🔄 AI分析额度限制\n\n📊 API使用量较大，建议您：\n- 稍等几分钟后重试\n- 或使用基础分析功能"
            else:
                return f"🤖 AI分析暂时不可用\n\n⚠️ 系统临时异常，请稍后重试\n错误信息: {str(e)[:100]}..."
    
    def _generate_analysis_summary(self, symbol: str, kline_data: pd.DataFrame,
                                 technical_indicators: Dict[str, Any], 
                                 trading_signals: Dict[str, str]) -> Dict[str, Any]:
        """生成分析摘要"""
        try:
            current_price = kline_data['close'].iloc[-1]
            prev_price = kline_data['close'].iloc[-2] if len(kline_data) >= 2 else current_price
            price_change = current_price - prev_price
            price_change_percent = (price_change / prev_price * 100) if prev_price != 0 else 0
            
            summary = {
                'symbol': symbol,
                'current_price': float(current_price),
                'price_change': float(price_change),
                'price_change_percent': float(price_change_percent),
                'data_quality': {
                    'kline_points': len(kline_data),
                    'indicators_count': len(technical_indicators),
                    'has_signals': len(trading_signals) > 0
                }
            }
            
            # 关键指标摘要
            key_indicators = {}
            indicator_keys = ['rsi', 'macd_trend', 'bb_signal', 'overall_signal']
            for key in indicator_keys:
                if key in technical_indicators:
                    key_indicators[key] = technical_indicators[key]
                elif key in trading_signals:
                    key_indicators[key] = trading_signals[key]
            
            summary['key_indicators'] = key_indicators
            
            # 风险等级评估
            risk_level = self._assess_risk_level(technical_indicators, trading_signals)
            summary['risk_assessment'] = risk_level
            
            return summary
            
        except Exception as e:
            logger.error(f"生成分析摘要失败: {str(e)}")
            return {'error': f"摘要生成失败: {str(e)}"}
    
    def _assess_risk_level(self, indicators: Dict[str, Any], 
                          signals: Dict[str, str]) -> str:
        """评估风险等级"""
        try:
            risk_score = 0
            
            # RSI风险评估
            if 'rsi' in indicators:
                rsi = indicators['rsi']
                if rsi >= 80 or rsi <= 20:
                    risk_score += 2
                elif rsi >= 70 or rsi <= 30:
                    risk_score += 1
            
            # 波动率风险评估
            if 'atr_percent' in indicators:
                atr_percent = indicators['atr_percent']
                if atr_percent >= 5:
                    risk_score += 2
                elif atr_percent >= 3:
                    risk_score += 1
            
            # 布林带风险评估
            if 'bb_signal' in indicators:
                bb_signal = indicators['bb_signal']
                if "超买" in bb_signal or "超卖" in bb_signal:
                    risk_score += 1
            
            # 成交量风险评估
            if 'volume_signal' in indicators:
                volume_signal = indicators['volume_signal']
                if "异常" in volume_signal:
                    risk_score += 1
            
            # 风险等级映射
            if risk_score >= 5:
                return "高风险"
            elif risk_score >= 3:
                return "中等风险"
            elif risk_score >= 1:
                return "低风险"
            else:
                return "正常"
                
        except Exception as e:
            logger.warning(f"风险评估失败: {str(e)}")
            return "未知"
    
    def get_supported_symbols(self) -> List[str]:
        """获取支持的币种列表"""
        return self.kline_fetcher.get_supported_symbols()
    
    def get_available_intervals(self) -> List[str]:
        """获取支持的时间周期"""
        return self.kline_fetcher.get_available_intervals()
    
    async def batch_analyze_coins(self, symbols: List[str], market_type: str = "futures",
                                interval: str = "1h", use_ai: bool = False) -> Dict[str, Any]:
        """批量分析多个币种"""
        try:
            logger.info(f"📊 开始批量分析 {len(symbols)} 个币种")
            
            results = {}
            errors = []
            
            # 并发分析（限制并发数避免API限制）
            semaphore = asyncio.Semaphore(3)  # 最多3个并发
            
            async def analyze_single(symbol):
                async with semaphore:
                    try:
                        result = await self.analyze_coin(symbol, market_type, interval, use_ai)
                        return symbol, result
                    except Exception as e:
                        logger.error(f"分析 {symbol} 失败: {str(e)}")
                        return symbol, {'success': False, 'error': str(e)}
            
            # 执行批量分析
            tasks = [analyze_single(symbol) for symbol in symbols]
            completed_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            successful_count = 0
            for result in completed_results:
                if isinstance(result, Exception):
                    errors.append(str(result))
                else:
                    symbol, analysis = result
                    results[symbol] = analysis
                    if analysis.get('success', False):
                        successful_count += 1
                    else:
                        errors.append(f"{symbol}: {analysis.get('error', '未知错误')}")
            
            logger.info(f"✅ 批量分析完成: {successful_count}/{len(symbols)} 成功")
            
            return {
                'success': True,
                'total_symbols': len(symbols),
                'successful_count': successful_count,
                'results': results,
                'errors': errors,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"批量分析失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


async def test_coin_query_manager():
    """测试币种查询管理器"""
    # 创建管理器实例
    manager = AICoinQueryManager()
    
    # 测试单个币种分析
    print("=== 测试单个币种分析 ===")
    result = await manager.analyze_coin('BTCUSDT', 'futures', '1h', use_ai=False)
    
    print(f"分析结果: {result['success']}")
    if result['success']:
        summary = result['data'].get('summary', {})
        print(f"当前价格: ${summary.get('current_price', 0):,.2f}")
        print(f"价格变化: {summary.get('price_change_percent', 0):+.2f}%")
        print(f"风险评估: {summary.get('risk_assessment', '未知')}")
        
        # 显示关键指标
        key_indicators = summary.get('key_indicators', {})
        for key, value in key_indicators.items():
            print(f"{key}: {value}")
    else:
        print(f"分析失败: {result.get('error', '未知错误')}")
    
    # 测试获取支持的币种
    print("\n=== 支持的币种列表 ===")
    symbols = manager.get_supported_symbols()
    print(f"支持的币种数量: {len(symbols)}")
    print(f"前10个币种: {symbols[:10]}")
    
    # 测试批量分析
    print("\n=== 测试批量分析 ===")
    test_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']
    batch_result = await manager.batch_analyze_coins(test_symbols, use_ai=False)
    
    print(f"批量分析结果: {batch_result['success']}")
    print(f"成功分析: {batch_result.get('successful_count', 0)}/{batch_result.get('total_symbols', 0)}")


if __name__ == "__main__":
    asyncio.run(test_coin_query_manager()) 