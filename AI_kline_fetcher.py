#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI币种查询功能 - K线数据获取器
负责调用CoinGlass API获取实时K线数据
"""

import os
import json
import time
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging

# 配置日志
logger = logging.getLogger(__name__)

class AIKlineFetcher:
    """AI K线数据获取器 - 基于CoinGlass API"""
    
    def __init__(self, cache_dir: str = "data/cache/coinglass"):
        """初始化K线数据获取器
        
        Args:
            cache_dir: 缓存目录路径
        """
        self.cache_dir = cache_dir
        self.headers = {
            "accept": "application/json",
            "CG-API-KEY": "c40fbbee201d4dfab3a4b62f37f0b610"
        }
        
        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)
        logger.info(f"✅ AI K线数据获取器初始化完成，缓存目录: {self.cache_dir}")
    
    def get_available_intervals(self) -> List[str]:
        """获取支持的K线时间间隔"""
        return ['1m', '3m', '5m', '15m', '30m', '1h', '4h', '6h', '8h', '12h', '1d', '1w']
    
    def fetch_futures_kline(self, symbol: str, interval: str = "1h",
                          limit: int = 200, exchange: str = "Binance") -> Tuple[bool, Optional[pd.DataFrame], str]:
        """获取合约K线数据

        Args:
            symbol: 交易对符号，如 'BTCUSDT'
            interval: K线间隔，如 '1h', '4h', '1d'
            limit: 获取数据条数，最大4500
            exchange: 交易所名称，默认Binance

        Returns:
            (成功标志, K线DataFrame, 状态信息)
        """
        try:
            logger.info(f"🔄 正在获取合约K线数据: {symbol} {interval}")

            # API端点
            url = "https://open-api-v4.coinglass.com/api/futures/price/history"

            # 请求参数
            params = {
                "exchange": exchange,
                "symbol": symbol,
                "interval": interval,
                "limit": limit
            }

            # 发送请求
            response = requests.get(url, headers=self.headers, params=params, timeout=30)

            if response.status_code == 200:
                data = response.json()

                if data.get("code") == "0" and "data" in data:
                    kline_data = data["data"]

                    if not kline_data:
                        return False, None, f"未获取到 {symbol} 的K线数据"

                    # 转换为DataFrame
                    df = pd.DataFrame(kline_data)

                    # 处理时间戳和数据类型
                    df['time'] = pd.to_datetime(df['time'], unit='ms')
                    df['open'] = pd.to_numeric(df['open'])
                    df['high'] = pd.to_numeric(df['high'])
                    df['low'] = pd.to_numeric(df['low'])
                    df['close'] = pd.to_numeric(df['close'])
                    df['volume_usd'] = pd.to_numeric(df['volume_usd'])

                    # 设置时间为索引
                    df.set_index('time', inplace=True)
                    df.sort_index(inplace=True)

                    # 🆕 K线完整性检测和数据质量分析
                    df = self._analyze_kline_completeness(df, interval)

                    logger.info(f"✅ 合约K线数据获取成功: {len(df)}条记录")
                    return True, df, f"成功获取{len(df)}条K线数据"

                else:
                    error_msg = f"API响应错误: {data.get('msg', '未知错误')}"
                    logger.error(error_msg)
                    return False, None, error_msg

            else:
                error_msg = f"HTTP请求失败: {response.status_code}"
                logger.error(error_msg)
                return False, None, error_msg

        except Exception as e:
            error_msg = f"获取合约K线数据异常: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg
    
    def fetch_spot_kline(self, symbol: str, interval: str = "1h", 
                        limit: int = 200, exchange: str = "Binance") -> Tuple[bool, Optional[pd.DataFrame], str]:
        """获取现货K线数据
        
        Args:
            symbol: 交易对符号，如 'BTCUSDT'
            interval: K线间隔，如 '1h', '4h', '1d'
            limit: 获取数据条数，最大4500
            exchange: 交易所名称，默认Binance
            
        Returns:
            (成功标志, K线DataFrame, 状态信息)
        """
        try:
            logger.info(f"🔄 正在获取现货K线数据: {symbol} {interval}")
            
            # API端点
            url = "https://open-api-v4.coinglass.com/api/spot/price/history"
            
            # 请求参数
            params = {
                "exchange": exchange,
                "symbol": symbol,
                "interval": interval,
                "limit": limit
            }
            
            # 发送请求
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get("code") == "0" and "data" in data:
                    kline_data = data["data"]
                    
                    if not kline_data:
                        return False, None, f"未获取到 {symbol} 的现货K线数据"
                    
                    # 转换为DataFrame
                    df = pd.DataFrame(kline_data)
                    
                    # 处理时间戳和数据类型
                    df['time'] = pd.to_datetime(df['time'], unit='ms')
                    df['open'] = pd.to_numeric(df['open'])
                    df['high'] = pd.to_numeric(df['high'])
                    df['low'] = pd.to_numeric(df['low'])
                    df['close'] = pd.to_numeric(df['close'])
                    df['volume_usd'] = pd.to_numeric(df['volume_usd'])
                    
                    # 设置时间为索引
                    df.set_index('time', inplace=True)
                    df.sort_index(inplace=True)
                    
                    logger.info(f"✅ 现货K线数据获取成功: {len(df)}条记录")
                    return True, df, f"成功获取{len(df)}条K线数据"
                    
                else:
                    error_msg = f"API响应错误: {data.get('msg', '未知错误')}"
                    logger.error(error_msg)
                    return False, None, error_msg
                    
            else:
                error_msg = f"HTTP请求失败: {response.status_code}"
                logger.error(error_msg)
                return False, None, error_msg
                
        except Exception as e:
            error_msg = f"获取现货K线数据异常: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg
    
    def cache_kline_data(self, symbol: str, market_type: str, interval: str, 
                        df: pd.DataFrame) -> bool:
        """缓存K线数据到本地
        
        Args:
            symbol: 交易对符号
            market_type: 市场类型 'futures' 或 'spot'
            interval: K线间隔
            df: K线数据DataFrame
            
        Returns:
            是否缓存成功
        """
        try:
            # 生成缓存文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{symbol}_{market_type}_{interval}_{timestamp}.json"
            filepath = os.path.join(self.cache_dir, filename)
            
            # 转换DataFrame为JSON格式
            cache_data = {
                "symbol": symbol,
                "market_type": market_type,
                "interval": interval,
                "timestamp": timestamp,
                "data_count": len(df),
                "kline_data": df.reset_index().to_dict('records')
            }
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"✅ K线数据缓存成功: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"❌ K线数据缓存失败: {str(e)}")
            return False
    
    def get_supported_symbols(self) -> List[str]:
        """获取支持的交易对列表（增强版 - 支持更多币种）"""
        try:
            # 优先从主机器人获取完整的活跃交易对列表
            try:
                # 避免循环导入：使用 importlib 延迟导入
                import importlib
                crypto_bot_module = importlib.import_module('crypto_trading_bot')
                TradingSuperheroBot = getattr(crypto_bot_module, 'TradingSuperheroBot', None)

                if TradingSuperheroBot:
                    bot = TradingSuperheroBot()
                    symbols = bot.get_active_symbols()

                    if symbols and len(symbols) > 0:
                        logger.info(f"✅ 从主机器人获取到 {len(symbols)} 个活跃交易对")
                        return symbols
                else:
                    logger.warning("⚠️ 无法找到TradingSuperheroBot类，使用备用方法")
            except Exception as e:
                logger.warning(f"从主机器人获取交易对失败，尝试备用方法: {e}")
            
            # 备用方法：读取现有的CoinGlass缓存数据
            data_dir = "data/coinglass"
            
            # 查找最新的缓存目录
            if os.path.exists(data_dir):
                cache_dirs = [d for d in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, d))]
                
                if cache_dirs:
                    cache_dirs.sort(reverse=True)  # 按时间倒序
                    latest_dir = os.path.join(data_dir, cache_dirs[0])
                    
                    # 读取futures数据
                    futures_file = os.path.join(latest_dir, "futures.json")
                    if os.path.exists(futures_file):
                        with open(futures_file, 'r', encoding='utf-8') as f:
                            futures_data = json.load(f)
                        
                        # 提取symbol列表，增加数量
                        symbols = [item['symbol'] + 'USDT' for item in futures_data if 'symbol' in item]
                        # 增加到200个热门币种（从50个增加）
                        filtered_symbols = symbols[:200]
                        logger.info(f"✅ 从CoinGlass缓存获取到 {len(filtered_symbols)} 个币种")
                        return filtered_symbols
            
            # 如果没有缓存数据，返回扩展的默认列表
            extended_default = [
                # 主流币种
                'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'ADAUSDT', 'XRPUSDT', 
                'DOTUSDT', 'LINKUSDT', 'LTCUSDT', 'MATICUSDT', 'DOGEUSDT', 'AVAXUSDT',
                'UNIUSDT', 'ATOMUSDT', 'ETCUSDT', 'XLMUSDT', 'BCHUSDT', 'FILUSDT',
                'TRXUSDT', 'EOSUSDT', 'AAVEUSDT', 'GRTUSDT', 'MKRUSDT', 'COMPUSDT',
                # 热门DeFi币种
                'SUSHIUSDT', 'YFIUSDT', 'SNXUSDT', 'CRVUSDT', '1INCHUSDT', 'ALPHAUSDT',
                'BANDUSDT', 'RENUSDT', 'STORJUSDT', 'KNCUSDT', 'ZRXUSDT', 'LRCUSDT',
                # Layer1/Layer2币种
                'NEARUSDT', 'FTMUSDT', 'ALGOUSDT', 'ZILUSDT', 'VETUSDT', 'ICPUSDT',
                'THETAUSDT', 'FLOWUSDT', 'XTZUSDT', 'EGLDUSDT', 'HBARUSDT', 'ONEUSDT',
                # 游戏/NFT币种
                'SANDUSDT', 'MANAUSDT', 'AXSUSDT', 'ENJUSDT', 'CHZUSDT', 'ALICEUSDT',
                # 隐私币种
                'ZECUSDT', 'DASHUSDT', 'ZENUSDT',
                # 其他热门币种
                'BATUSDT', 'OMGUSDT', 'RLCUSDT', 'FETUSDT', 'CTSIUSDT', 'OCEANUSDT',
                'CTKUSDT', 'AKROUSDT', 'CHRUSDT', 'ANTUSDT', 'RUNEUSDT', 'KAVAUSDT',
                'SXPUSDT', 'DEFIUSDT', 'TRBUSDT', 'SKLUSDT', 'ANKRUSDT', 'BELUSDT',
                'RVNUSDT', 'SFPUSDT', 'COTIUSDT', 'DENTUSDT', 'CELRUSDT', 'HOTUSDT',
                'MTLUSDT', 'OGNUSDT', 'NKNUSDT', '1000SHIBUSDT', 'BAKEUSDT', 'GTCUSDT',
                'IOTXUSDT', 'C98USDT', 'MASKUSDT', 'ATAUSDT', 'DYDXUSDT', '1000XECUSDT',
                'GALAUSDT', 'CELOUSDT', 'ARUSDT', 'ARPAUSDT', 'LPTUSDT', 'ENSUSDT',
                'PEOPLEUSDT', 'ROSEUSDT', 'RSRUSDT', 'FLMUSDT', 'KSMUSDT', 'ICXUSDT',
                # 新增热门币种
                'APUSDT', 'ENAUSDT', 'PENDLEUSDT', 'ARBUSDT', 'OPUSDT', 'WLDUSDT',
                'LDOUSDT', 'STXUSDT', 'INJUSDT', 'TKXUSDT', 'SUIUSDT', 'APTUSDT',
                'WUSDT', 'PEPEUSDT', 'SEIUSDT', 'JUPUSDT', 'PYRUSDT', 'TIAUSDT',
                'ORDIUSDT', 'WIFUSDT', 'BONKUSDT', 'JUPUSDT', 'RAYUSDT'
            ]
            
            logger.warning(f"使用扩展默认列表: {len(extended_default)} 个币种")
            return extended_default
            
        except Exception as e:
            logger.error(f"获取支持的交易对列表失败: {str(e)}")
            return ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'BNBUSDT', 'ADAUSDT']  # 返回基础列表
    
    def fetch_basis_history(self, symbol: str, interval: str = "1h", 
                           limit: int = 100, exchange: str = "Binance") -> Tuple[bool, Optional[pd.DataFrame], str]:
        """获取合约基差历史数据
        
        Args:
            symbol: 交易对符号，如 'BTCUSDT'
            interval: 时间间隔，如 '1h', '4h', '1d'
            limit: 获取数据条数，最大4500
            exchange: 交易所名称，默认Binance
            
        Returns:
            (成功标志, 基差DataFrame, 状态信息)
        """
        try:
            logger.info(f"🔄 正在获取基差历史数据: {symbol} {interval}")
            
            # API端点
            url = "https://open-api-v4.coinglass.com/api/futures/basis/history"
            
            # 请求参数
            params = {
                "exchange": exchange,
                "symbol": symbol,
                "interval": interval,
                "limit": limit
            }
            
            # 发送请求
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get("code") == "0" and "data" in data:
                    basis_data = data["data"]
                    
                    if not basis_data:
                        return False, None, f"未获取到 {symbol} 的基差数据"
                    
                    # 转换为DataFrame
                    df = pd.DataFrame(basis_data)
                    
                    # 处理时间戳和数据类型
                    df['time'] = pd.to_datetime(df['time'], unit='ms')
                    df['open_basis'] = pd.to_numeric(df['open_basis'])
                    df['close_basis'] = pd.to_numeric(df['close_basis'])
                    df['open_change'] = pd.to_numeric(df['open_change'])
                    df['close_change'] = pd.to_numeric(df['close_change'])
                    
                    # 设置时间为索引
                    df.set_index('time', inplace=True)
                    df.sort_index(inplace=True)
                    
                    logger.info(f"✅ 基差历史数据获取成功: {len(df)}条记录")
                    return True, df, f"成功获取{len(df)}条基差数据"
                    
                else:
                    error_msg = f"基差API响应错误: {data.get('msg', '未知错误')}"
                    logger.error(error_msg)
                    return False, None, error_msg
                    
            else:
                error_msg = f"基差API HTTP请求失败: {response.status_code}"
                logger.error(error_msg)
                return False, None, error_msg
                
        except Exception as e:
            error_msg = f"获取基差历史数据异常: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg
    
    def analyze_basis_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析基差数据
        
        Args:
            df: 基差数据DataFrame
            
        Returns:
            基差分析结果字典
        """
        try:
            if df is None or len(df) == 0:
                return {"error": "基差数据为空"}
            
            analysis = {}
            
            # 当前基差信息
            current_basis = df['close_basis'].iloc[-1]
            analysis['current_basis'] = float(current_basis)
            
            # 基差统计
            analysis['avg_basis'] = float(df['close_basis'].mean())
            analysis['max_basis'] = float(df['close_basis'].max())
            analysis['min_basis'] = float(df['close_basis'].min())
            analysis['std_basis'] = float(df['close_basis'].std())
            
            # 基差趋势分析
            if len(df) >= 2:
                prev_basis = df['close_basis'].iloc[-2]
                basis_change = current_basis - prev_basis
                analysis['basis_change'] = float(basis_change)
                analysis['basis_change_percent'] = float((basis_change / abs(prev_basis) * 100) if prev_basis != 0 else 0)
            
            # 市场情绪判断
            if current_basis > 0.1:
                analysis['market_sentiment'] = "极度乐观"
                analysis['sentiment_score'] = 5
            elif current_basis > 0.05:
                analysis['market_sentiment'] = "偏向乐观"
                analysis['sentiment_score'] = 4
            elif current_basis > 0:
                analysis['market_sentiment'] = "轻微乐观"
                analysis['sentiment_score'] = 3
            elif current_basis > -0.05:
                analysis['market_sentiment'] = "轻微悲观"
                analysis['sentiment_score'] = 2
            elif current_basis > -0.1:
                analysis['market_sentiment'] = "偏向悲观"
                analysis['sentiment_score'] = 1
            else:
                analysis['market_sentiment'] = "极度悲观"
                analysis['sentiment_score'] = 0
            
            # 套利机会评估
            abs_basis = abs(current_basis)
            if abs_basis > 0.15:
                analysis['arbitrage_opportunity'] = "高"
                analysis['arbitrage_signal'] = f"基差较大({abs_basis:.4f}%)，存在明显套利机会"
            elif abs_basis > 0.08:
                analysis['arbitrage_opportunity'] = "中等"
                analysis['arbitrage_signal'] = f"基差适中({abs_basis:.4f}%)，可能存在套利机会"
            else:
                analysis['arbitrage_opportunity'] = "低"
                analysis['arbitrage_signal'] = f"基差较小({abs_basis:.4f}%)，套利机会有限"
            
            # 基差波动性分析
            if analysis['std_basis'] > 0.05:
                analysis['volatility_level'] = "高波动"
            elif analysis['std_basis'] > 0.02:
                analysis['volatility_level'] = "中等波动"
            else:
                analysis['volatility_level'] = "低波动"
            
            # 基差趋势强度
            if len(df) >= 5:
                recent_trend = df['close_basis'].tail(5).diff().mean()
                if abs(recent_trend) > 0.01:
                    analysis['trend_strength'] = "强趋势"
                elif abs(recent_trend) > 0.005:
                    analysis['trend_strength'] = "中等趋势"
                else:
                    analysis['trend_strength'] = "弱趋势或震荡"
                
                analysis['trend_direction'] = "上升" if recent_trend > 0 else "下降" if recent_trend < 0 else "平稳"
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析基差数据失败: {str(e)}")
            return {"error": f"基差分析失败: {str(e)}"}
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证交易对符号是否有效"""
        if not symbol:
            return False

        # 基本格式检查
        if not symbol.endswith('USDT'):
            return False

        # 检查是否在支持列表中
        supported_symbols = self.get_supported_symbols()
        return symbol.upper() in [s.upper() for s in supported_symbols]

    def _analyze_kline_completeness(self, df: pd.DataFrame, interval: str) -> pd.DataFrame:
        """分析K线数据完整性并添加标记

        Args:
            df: K线数据DataFrame
            interval: K线周期

        Returns:
            添加了完整性标记的DataFrame
        """
        try:
            if df is None or len(df) == 0:
                return df

            # 获取当前时间
            current_time = datetime.now()

            # 计算K线周期的秒数
            interval_seconds = self._get_interval_seconds(interval)

            # 分析最后一根K线是否完整
            last_kline_time = df.index[-1]

            # 计算最后一根K线应该结束的时间
            expected_end_time = last_kline_time + timedelta(seconds=interval_seconds)

            # 判断最后一根K线是否完整
            is_last_complete = current_time >= expected_end_time

            # 添加完整性标记列
            df['is_complete'] = True
            df.iloc[-1, df.columns.get_loc('is_complete')] = is_last_complete

            # 分析成交量异常
            df = self._analyze_volume_anomalies(df)

            # 添加数据质量评分
            df = self._calculate_data_quality_score(df)

            # 记录分析结果
            incomplete_count = len(df[~df['is_complete']])
            low_volume_count = len(df[df.get('volume_anomaly', False)])

            logger.info(f"📊 K线完整性分析: 总计{len(df)}根, 未完成{incomplete_count}根, 异常成交量{low_volume_count}根")

            return df

        except Exception as e:
            logger.error(f"❌ K线完整性分析失败: {str(e)}")
            return df

    def _get_interval_seconds(self, interval: str) -> int:
        """获取K线周期对应的秒数"""
        interval_map = {
            '1m': 60,
            '3m': 180,
            '5m': 300,
            '15m': 900,
            '30m': 1800,
            '1h': 3600,
            '4h': 14400,
            '6h': 21600,
            '8h': 28800,
            '12h': 43200,
            '1d': 86400,
            '1w': 604800
        }
        return interval_map.get(interval, 3600)  # 默认1小时

    def _analyze_volume_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """分析成交量异常情况"""
        try:
            if 'volume_usd' not in df.columns:
                return df

            # 计算成交量统计信息
            volume_mean = df['volume_usd'].mean()
            volume_std = df['volume_usd'].std()
            volume_median = df['volume_usd'].median()

            # 定义异常阈值（低于中位数的20%或低于均值的2个标准差）
            low_threshold = min(volume_median * 0.2, volume_mean - 2 * volume_std)

            # 标记异常成交量
            df['volume_anomaly'] = df['volume_usd'] < low_threshold

            # 计算成交量相对强度
            df['volume_strength'] = df['volume_usd'] / volume_mean

            # 标记成交量等级
            df['volume_level'] = 'normal'
            df.loc[df['volume_usd'] < volume_median * 0.5, 'volume_level'] = 'low'
            df.loc[df['volume_usd'] > volume_median * 2, 'volume_level'] = 'high'
            df.loc[df['volume_usd'] < volume_median * 0.1, 'volume_level'] = 'extremely_low'

            return df

        except Exception as e:
            logger.error(f"❌ 成交量异常分析失败: {str(e)}")
            return df

    def _calculate_data_quality_score(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算数据质量评分"""
        try:
            # 初始化质量评分
            df['quality_score'] = 1.0

            # 根据完整性调整评分
            if 'is_complete' in df.columns:
                df.loc[~df['is_complete'], 'quality_score'] *= 0.8

            # 根据成交量异常调整评分
            if 'volume_anomaly' in df.columns:
                df.loc[df['volume_anomaly'], 'quality_score'] *= 0.7

            # 根据成交量等级调整评分
            if 'volume_level' in df.columns:
                df.loc[df['volume_level'] == 'extremely_low', 'quality_score'] *= 0.5
                df.loc[df['volume_level'] == 'low', 'quality_score'] *= 0.8

            # 添加整体数据质量标记
            avg_quality = df['quality_score'].mean()
            df['overall_quality'] = 'high' if avg_quality > 0.9 else 'medium' if avg_quality > 0.7 else 'low'

            return df

        except Exception as e:
            logger.error(f"❌ 数据质量评分计算失败: {str(e)}")
            return df


def test_kline_fetcher():
    """测试K线数据获取器"""
    fetcher = AIKlineFetcher()
    
    # 测试获取BTC合约K线数据
    success, df, msg = fetcher.fetch_futures_kline("BTCUSDT", "1h", 100)
    print(f"合约K线测试结果: {success}, {msg}")
    if success and df is not None:
        print(f"数据行数: {len(df)}")
        print(f"最新价格: {df['close'].iloc[-1]}")
        print(f"数据时间范围: {df.index[0]} 到 {df.index[-1]}")
    
    # 测试获取BTC现货K线数据
    success, df, msg = fetcher.fetch_spot_kline("BTCUSDT", "1h", 100)
    print(f"现货K线测试结果: {success}, {msg}")
    if success and df is not None:
        print(f"数据行数: {len(df)}")
        print(f"最新价格: {df['close'].iloc[-1]}")


if __name__ == "__main__":
    test_kline_fetcher() 