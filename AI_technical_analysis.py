#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI币种查询功能 - 技术分析模块
使用pandas_ta库计算各种技术指标，支持多周期分析
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
import logging

# 尝试导入pandas_ta，如果没有安装则使用基础计算
try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
    print("✅ pandas_ta库可用")
except ImportError as e:
    PANDAS_TA_AVAILABLE = False
    print(f"⚠️ pandas_ta未安装，将使用基础技术指标计算: {e}")
    logging.warning(f"⚠️ pandas_ta未安装，将使用基础技术指标计算: {e}")

logger = logging.getLogger(__name__)

class AITechnicalAnalysis:
    """AI技术分析器 - 计算各种技术指标，支持多周期分析"""
    
    def __init__(self):
        """初始化技术分析器"""
        self.indicators = {}
        # 定义多周期参数
        self.bollinger_periods = list(range(10, 101, 10))  # 布林带周期：10, 20, 30, ..., 100
        self.ma_periods = [5, 10, 15, 20, 25, 30, 50, 100, 200]  # MA周期
        self.ema_periods = [5, 10, 12, 15, 20, 26, 30, 50, 100, 200]  # EMA周期
        self.rsi_periods = [7, 14, 21, 28]  # RSI周期
        self.macd_configs = [
            {'fast': 12, 'slow': 26, 'signal': 9},  # 标准MACD
            {'fast': 5, 'slow': 35, 'signal': 5},   # 快速MACD
            {'fast': 19, 'slow': 39, 'signal': 9}   # 慢速MACD
        ]
        logger.info("✅ AI技术分析器初始化完成（支持多周期分析）")
    
    def analyze_basis_indicators(self, basis_df: pd.DataFrame) -> Dict[str, Any]:
        """分析基差技术指标
        
        Args:
            basis_df: 基差历史数据DataFrame
            
        Returns:
            基差技术分析结果
        """
        try:
            if basis_df is None or len(basis_df) == 0:
                return {"error": "基差数据为空"}
            
            analysis = {}
            
            # 基本统计信息
            current_basis = float(basis_df['close_basis'].iloc[-1])
            avg_basis = float(basis_df['close_basis'].mean())
            max_basis = float(basis_df['close_basis'].max())
            min_basis = float(basis_df['close_basis'].min())
            std_basis = float(basis_df['close_basis'].std())
            
            analysis.update({
                'current_basis': current_basis,
                'avg_basis': avg_basis,
                'max_basis': max_basis,
                'min_basis': min_basis,
                'std_basis': std_basis
            })
            
            # 基差移动平均线
            if len(basis_df) >= 20:
                basis_df['basis_ma5'] = basis_df['close_basis'].rolling(window=5).mean()
                basis_df['basis_ma10'] = basis_df['close_basis'].rolling(window=10).mean()
                basis_df['basis_ma20'] = basis_df['close_basis'].rolling(window=20).mean()
                
                analysis['basis_ma5'] = float(basis_df['basis_ma5'].iloc[-1])
                analysis['basis_ma10'] = float(basis_df['basis_ma10'].iloc[-1])
                analysis['basis_ma20'] = float(basis_df['basis_ma20'].iloc[-1])
                
                # 基差趋势判断（基于移动平均线）
                if current_basis > analysis['basis_ma5'] > analysis['basis_ma10']:
                    analysis['basis_trend'] = "强劲上升"
                elif current_basis > analysis['basis_ma5']:
                    analysis['basis_trend'] = "温和上升"
                elif current_basis < analysis['basis_ma5'] < analysis['basis_ma10']:
                    analysis['basis_trend'] = "强劲下降"
                elif current_basis < analysis['basis_ma5']:
                    analysis['basis_trend'] = "温和下降"
                else:
                    analysis['basis_trend'] = "横盘整理"
            
            # 基差动能分析（基于变化率）
            if len(basis_df) >= 5:
                recent_changes = basis_df['close_change'].tail(5)
                avg_change = float(recent_changes.mean())
                
                if avg_change > 10:
                    analysis['basis_momentum'] = "强烈扩张"
                elif avg_change > 5:
                    analysis['basis_momentum'] = "适度扩张"
                elif avg_change > -5:
                    analysis['basis_momentum'] = "稳定"
                elif avg_change > -10:
                    analysis['basis_momentum'] = "适度收缩"
                else:
                    analysis['basis_momentum'] = "强烈收缩"
                
                analysis['avg_change_rate'] = avg_change
            
            # 市场情绪综合评分（-100到100）
            sentiment_score = 0
            
            # 基差绝对值评分
            if current_basis > 0.15:
                sentiment_score += 40
            elif current_basis > 0.05:
                sentiment_score += 20
            elif current_basis > 0:
                sentiment_score += 10
            elif current_basis > -0.05:
                sentiment_score -= 10
            elif current_basis > -0.15:
                sentiment_score -= 20
            else:
                sentiment_score -= 40
            
            # 基差趋势评分
            if 'basis_trend' in analysis:
                if "强劲上升" in analysis['basis_trend']:
                    sentiment_score += 30
                elif "温和上升" in analysis['basis_trend']:
                    sentiment_score += 15
                elif "强劲下降" in analysis['basis_trend']:
                    sentiment_score -= 30
                elif "温和下降" in analysis['basis_trend']:
                    sentiment_score -= 15
            
            # 动能评分
            if 'basis_momentum' in analysis:
                if "强烈扩张" in analysis['basis_momentum']:
                    sentiment_score += 20
                elif "适度扩张" in analysis['basis_momentum']:
                    sentiment_score += 10
                elif "强烈收缩" in analysis['basis_momentum']:
                    sentiment_score -= 20
                elif "适度收缩" in analysis['basis_momentum']:
                    sentiment_score -= 10
            
            # 限制评分范围
            sentiment_score = max(-100, min(100, sentiment_score))
            analysis['sentiment_score'] = sentiment_score
            
            # 市场情绪描述
            if sentiment_score >= 60:
                analysis['market_sentiment'] = "极度乐观"
                analysis['sentiment_emoji'] = "🚀"
            elif sentiment_score >= 30:
                analysis['market_sentiment'] = "明显乐观"
                analysis['sentiment_emoji'] = "📈"
            elif sentiment_score >= 10:
                analysis['market_sentiment'] = "轻微乐观"
                analysis['sentiment_emoji'] = "🟢"
            elif sentiment_score >= -10:
                analysis['market_sentiment'] = "中性偏谨慎"
                analysis['sentiment_emoji'] = "⚪"
            elif sentiment_score >= -30:
                analysis['market_sentiment'] = "轻微悲观"
                analysis['sentiment_emoji'] = "🔴"
            elif sentiment_score >= -60:
                analysis['market_sentiment'] = "明显悲观"
                analysis['sentiment_emoji'] = "📉"
            else:
                analysis['market_sentiment'] = "极度悲观"
                analysis['sentiment_emoji'] = "💥"
            
            # 套利机会分析
            abs_basis = abs(current_basis)
            if abs_basis > 0.2:
                analysis['arbitrage_level'] = "极高"
                analysis['arbitrage_description'] = f"基差达到{abs_basis:.3f}%，存在显著套利机会"
                analysis['arbitrage_emoji'] = "💰"
            elif abs_basis > 0.1:
                analysis['arbitrage_level'] = "高"
                analysis['arbitrage_description'] = f"基差为{abs_basis:.3f}%，存在较好套利机会"
                analysis['arbitrage_emoji'] = "💵"
            elif abs_basis > 0.05:
                analysis['arbitrage_level'] = "中等"
                analysis['arbitrage_description'] = f"基差为{abs_basis:.3f}%，存在一定套利空间"
                analysis['arbitrage_emoji'] = "💲"
            else:
                analysis['arbitrage_level'] = "低"
                analysis['arbitrage_description'] = f"基差仅{abs_basis:.3f}%，套利机会有限"
                analysis['arbitrage_emoji'] = "📊"
            
            # 波动性风险评估
            volatility_percentile = (std_basis - min_basis) / (max_basis - min_basis) * 100 if max_basis != min_basis else 50
            
            if volatility_percentile > 80:
                analysis['volatility_risk'] = "极高"
                analysis['risk_warning'] = "基差波动剧烈，存在高风险"
            elif volatility_percentile > 60:
                analysis['volatility_risk'] = "高"
                analysis['risk_warning'] = "基差波动较大，需谨慎操作"
            elif volatility_percentile > 40:
                analysis['volatility_risk'] = "中等"
                analysis['risk_warning'] = "基差波动正常，风险可控"
            else:
                analysis['volatility_risk'] = "低"
                analysis['risk_warning'] = "基差相对稳定，风险较低"
            
            logger.info(f"✅ 基差技术分析完成，情绪评分: {sentiment_score}")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 基差技术分析失败: {str(e)}")
            return {"error": f"基差分析异常: {str(e)}"}
    
    def get_basis_trading_signals(self, basis_analysis: Dict[str, Any]) -> Dict[str, str]:
        """基于基差分析生成交易信号
        
        Args:
            basis_analysis: 基差分析结果
            
        Returns:
            基差交易信号字典
        """
        try:
            signals = {}
            
            if 'error' in basis_analysis:
                signals['基差信号'] = "数据不可用"
                return signals
            
            current_basis = basis_analysis.get('current_basis', 0)
            sentiment_score = basis_analysis.get('sentiment_score', 0)
            basis_trend = basis_analysis.get('basis_trend', '')
            arbitrage_level = basis_analysis.get('arbitrage_level', '低')
            
            # 主要基差信号
            if current_basis > 0.15:
                signals['基差信号'] = "强烈正基差，市场极度乐观"
            elif current_basis > 0.05:
                signals['基差信号'] = "明显正基差，市场偏向乐观"
            elif current_basis > 0:
                signals['基差信号'] = "轻微正基差，市场谨慎乐观"
            elif current_basis > -0.05:
                signals['基差信号'] = "轻微负基差，市场谨慎悲观"
            elif current_basis > -0.15:
                signals['基差信号'] = "明显负基差，市场偏向悲观"
            else:
                signals['基差信号'] = "强烈负基差，市场极度悲观"
            
            # 趋势信号
            if "上升" in basis_trend:
                signals['基差趋势'] = f"基差{basis_trend}，期现价差扩大"
            elif "下降" in basis_trend:
                signals['基差趋势'] = f"基差{basis_trend}，期现价差收窄"
            else:
                signals['基差趋势'] = "基差横盘，期现价差稳定"
            
            # 套利信号
            if arbitrage_level in ['极高', '高']:
                signals['套利机会'] = f"{arbitrage_level}套利机会，建议关注期现套利"
            elif arbitrage_level == '中等':
                signals['套利机会'] = "存在一定套利空间，可适度参与"
            else:
                signals['套利机会'] = "套利机会有限，以观望为主"
            
            # 综合操作建议
            if sentiment_score >= 30 and "上升" in basis_trend:
                signals['操作建议'] = "市场情绪乐观且基差上升，可考虑做多"
            elif sentiment_score <= -30 and "下降" in basis_trend:
                signals['操作建议'] = "市场情绪悲观且基差下降，可考虑做空"
            elif abs(current_basis) > 0.1:
                signals['操作建议'] = "基差较大，重点关注套利机会"
            else:
                signals['操作建议'] = "基差相对稳定，以观望或小仓位操作为主"
            
            # 风险提示
            volatility_risk = basis_analysis.get('volatility_risk', '中等')
            if volatility_risk in ['极高', '高']:
                signals['风险提示'] = f"基差{volatility_risk}波动，请严格控制仓位和风险"
            else:
                signals['风险提示'] = "基差波动正常，注意合理止损"
            
            return signals
            
        except Exception as e:
            logger.error(f"❌ 生成基差交易信号失败: {str(e)}")
            return {"基差信号": "信号生成失败"}


    def calculate_all_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算所有技术指标
        
        Args:
            df: K线数据DataFrame，需包含OHLCV数据
            
        Returns:
            包含所有技术指标的字典
        """
        if df is None or len(df) < 20:
            logger.warning("数据不足，无法计算技术指标")
            return {}
        
        try:
            indicators = {}
            
            # 基础价格指标
            indicators.update(self._calculate_price_indicators(df))
            
            # 移动平均线
            indicators.update(self._calculate_moving_averages(df))
            
            # 动量指标
            indicators.update(self._calculate_momentum_indicators(df))
            
            # 波动率指标
            indicators.update(self._calculate_volatility_indicators(df))
            
            # 趋势指标
            indicators.update(self._calculate_trend_indicators(df))
            
            # 成交量指标
            indicators.update(self._calculate_volume_indicators(df))
            
            # 添加向后兼容的指标别名
            indicators.update(self._add_compatibility_indicators(indicators))
            
            logger.info(f"✅ 计算了 {len(indicators)} 个技术指标（包含多周期分析）")
            return indicators
            
        except Exception as e:
            logger.error(f"❌ 计算技术指标失败: {str(e)}")
            return {}
    
    def _calculate_price_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算基础价格指标"""
        indicators = {}
        
        # 当前价格信息
        current_price = df['close'].iloc[-1]
        prev_price = df['close'].iloc[-2] if len(df) >= 2 else current_price
        
        indicators['current_price'] = float(current_price)
        indicators['price_change'] = float(current_price - prev_price)
        indicators['price_change_percent'] = float((current_price - prev_price) / prev_price * 100) if prev_price != 0 else 0
        
        # 最高最低价
        indicators['period_high'] = float(df['high'].max())
        indicators['period_low'] = float(df['low'].min())
        indicators['period_volume'] = float(df['volume_usd'].sum())
        
        # 价格位置
        high_low_range = indicators['period_high'] - indicators['period_low']
        if high_low_range > 0:
            indicators['price_position'] = float((current_price - indicators['period_low']) / high_low_range * 100)
        else:
            indicators['price_position'] = 50.0
        
        return indicators
    
    def _calculate_moving_averages(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算移动平均线"""
        indicators = {}
        
        try:
            close = df['close']
            current_price = close.iloc[-1]
            
            # 简单移动平均线
            for period in self.ma_periods:
                if len(df) >= period:
                    if PANDAS_TA_AVAILABLE:
                        ma = ta.sma(close, length=period)
                    else:
                        ma = close.rolling(window=period).mean()
                    
                    if not ma.empty:
                        ma_value = float(ma.iloc[-1])
                        indicators[f'ma_{period}'] = ma_value
                        indicators[f'ma_{period}_diff'] = float(current_price - ma_value)
                        indicators[f'ma_{period}_diff_percent'] = float((current_price - ma_value) / ma_value * 100) if ma_value != 0 else 0
            
            # 指数移动平均线
            for period in self.ema_periods:
                if len(df) >= period:
                    if PANDAS_TA_AVAILABLE:
                        ema = ta.ema(close, length=period)
                    else:
                        ema = close.ewm(span=period).mean()
                    
                    if not ema.empty:
                        ema_value = float(ema.iloc[-1])
                        indicators[f'ema_{period}'] = ema_value
                        indicators[f'ema_{period}_diff'] = float(current_price - ema_value)
                        indicators[f'ema_{period}_diff_percent'] = float((current_price - ema_value) / ema_value * 100) if ema_value != 0 else 0
        
        except Exception as e:
            logger.warning(f"计算移动平均线失败: {str(e)}")
        
        return indicators
    
    def _calculate_momentum_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算动量指标"""
        indicators = {}
        
        try:
            close = df['close']
            high = df['high']
            low = df['low']
            
            # RSI (相对强弱指数)
            for period in self.rsi_periods:
                if len(df) >= period:
                    if PANDAS_TA_AVAILABLE:
                        rsi = ta.rsi(close, length=period)
                    else:
                        rsi = self._calculate_rsi(close, period)
                    
                    if rsi is not None and not rsi.empty:
                        indicators[f'rsi_{period}'] = float(rsi.iloc[-1])
                        
                        # RSI信号判断
                        rsi_value = indicators[f'rsi_{period}']
                        if rsi_value >= 70:
                            indicators[f'rsi_signal_{period}'] = "超买"
                        elif rsi_value <= 30:
                            indicators[f'rsi_signal_{period}'] = "超卖"
                        else:
                            indicators[f'rsi_signal_{period}'] = "中性"
            
            # MACD
            for config in self.macd_configs:
                if len(df) >= max(config['fast'], config['slow']):
                    if PANDAS_TA_AVAILABLE:
                        macd_data = ta.macd(close, fast=config['fast'], slow=config['slow'], signal=config['signal'])
                        if macd_data is not None and len(macd_data.columns) >= 3:
                            indicators[f'macd_{config["fast"]}_{config["slow"]}_{config["signal"]}'] = float(macd_data.iloc[-1, 0])
                            indicators[f'macd_signal_{config["fast"]}_{config["slow"]}_{config["signal"]}'] = float(macd_data.iloc[-1, 1])
                            indicators[f'macd_histogram_{config["fast"]}_{config["slow"]}_{config["signal"]}'] = float(macd_data.iloc[-1, 2])
                            
                            # MACD信号判断
                            if indicators[f'macd_{config["fast"]}_{config["slow"]}_{config["signal"]}'] > indicators[f'macd_signal_{config["fast"]}_{config["slow"]}_{config["signal"]}']:
                                indicators[f'macd_trend_{config["fast"]}_{config["slow"]}_{config["signal"]}'] = "看涨"
                            else:
                                indicators[f'macd_trend_{config["fast"]}_{config["slow"]}_{config["signal"]}'] = "看跌"
                    else:
                        macd_result = self._calculate_macd(close, **config)
                        indicators.update(macd_result)
            
            # 随机指标 (Stochastic)
            if len(df) >= 14:
                if PANDAS_TA_AVAILABLE:
                    stoch = ta.stoch(high, low, close)
                    if stoch is not None and len(stoch.columns) >= 2:
                        indicators['stoch_k'] = float(stoch.iloc[-1, 0])
                        indicators['stoch_d'] = float(stoch.iloc[-1, 1])
                        
                        # 随机指标信号判断
                        if indicators['stoch_k'] >= 80:
                            indicators['stoch_signal'] = "超买"
                        elif indicators['stoch_k'] <= 20:
                            indicators['stoch_signal'] = "超卖"
                        else:
                            indicators['stoch_signal'] = "中性"
            
            # 威廉指标 (Williams %R)
            if len(df) >= 14:
                if PANDAS_TA_AVAILABLE:
                    willr = ta.willr(high, low, close, length=14)
                    if willr is not None and not willr.empty:
                        indicators['williams_r'] = float(willr.iloc[-1])
        
        except Exception as e:
            logger.warning(f"计算动量指标失败: {str(e)}")
        
        return indicators
    
    def _calculate_volatility_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算波动率指标"""
        indicators = {}
        
        try:
            close = df['close']
            high = df['high']
            low = df['low']
            
            # 布林带 (Bollinger Bands)
            for period in self.bollinger_periods:
                if len(df) >= period:
                    try:
                        if PANDAS_TA_AVAILABLE:
                            bbands = ta.bbands(close, length=period, std=2)
                            if bbands is not None and not bbands.empty:
                                # pandas_ta布林带返回格式: BBL_period_2.0, BBM_period_2.0, BBU_period_2.0
                                cols = bbands.columns
                                lower_col = f'BBL_{period}_2.0'
                                middle_col = f'BBM_{period}_2.0'
                                upper_col = f'BBU_{period}_2.0'
                                
                                if all(col in cols for col in [lower_col, middle_col, upper_col]):
                                    indicators[f'bb_upper_{period}'] = float(bbands[upper_col].iloc[-1])
                                    indicators[f'bb_middle_{period}'] = float(bbands[middle_col].iloc[-1])
                                    indicators[f'bb_lower_{period}'] = float(bbands[lower_col].iloc[-1])
                                elif len(cols) >= 3:
                                    # 备用方案：按列索引
                                    indicators[f'bb_lower_{period}'] = float(bbands.iloc[-1, 0])
                                    indicators[f'bb_middle_{period}'] = float(bbands.iloc[-1, 1])
                                    indicators[f'bb_upper_{period}'] = float(bbands.iloc[-1, 2])
                                else:
                                    # 如果pandas_ta格式不符合预期，使用手动计算
                                    bb_result = self._calculate_bollinger_bands(close, period)
                                    indicators.update(bb_result)
                                    continue
                            else:
                                # 如果pandas_ta返回空，使用手动计算
                                bb_result = self._calculate_bollinger_bands(close, period)
                                indicators.update(bb_result)
                                continue
                        else:
                            # 如果pandas_ta不可用，使用手动计算
                            bb_result = self._calculate_bollinger_bands(close, period)
                            indicators.update(bb_result)
                            continue
                        
                        # 计算布林带位置和信号（如果上轨下轨已经计算出来）
                        if f'bb_upper_{period}' in indicators and f'bb_lower_{period}' in indicators:
                            current_price = close.iloc[-1]
                            bb_width = indicators[f'bb_upper_{period}'] - indicators[f'bb_lower_{period}']
                            
                            if bb_width > 0:
                                position = (current_price - indicators[f'bb_lower_{period}']) / bb_width * 100
                                indicators[f'bb_position_{period}'] = float(position)
                            else:
                                indicators[f'bb_position_{period}'] = 50.0  # 默认中间位置
                            
                            # 布林带信号
                            if current_price >= indicators[f'bb_upper_{period}']:
                                indicators[f'bb_signal_{period}'] = "超买(触及上轨)"
                            elif current_price <= indicators[f'bb_lower_{period}']:
                                indicators[f'bb_signal_{period}'] = "超卖(触及下轨)"
                            else:
                                indicators[f'bb_signal_{period}'] = "正常区间"
                        
                    except Exception as e:
                        logger.warning(f"计算布林带BB({period})失败: {str(e)}，使用手动计算")
                        # 发生错误时使用手动计算作为备份
                        bb_result = self._calculate_bollinger_bands(close, period)
                        indicators.update(bb_result)
            
            # ATR (平均真实波动范围)
            if len(df) >= 14:
                try:
                    if PANDAS_TA_AVAILABLE:
                        atr = ta.atr(high, low, close, length=14)
                        if atr is not None and not atr.empty:
                            indicators['atr'] = float(atr.iloc[-1])
                            # ATR百分比
                            current_price = close.iloc[-1]
                            indicators['atr_percent'] = float(indicators['atr'] / current_price * 100) if current_price != 0 else 0
                except Exception as e:
                    logger.warning(f"计算ATR失败: {str(e)}")
        
        except Exception as e:
            logger.warning(f"计算波动率指标失败: {str(e)}")
        
        return indicators
    
    def _calculate_trend_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算趋势指标"""
        indicators = {}
        
        try:
            close = df['close']
            high = df['high']
            low = df['low']
            
            # ADX (平均方向性指数)
            if len(df) >= 14:
                if PANDAS_TA_AVAILABLE:
                    adx = ta.adx(high, low, close, length=14)
                    if adx is not None and not adx.empty:
                        if len(adx.columns) >= 1:
                            indicators['adx'] = float(adx.iloc[-1, 0])
                            
                            # ADX趋势强度判断
                            adx_value = indicators['adx']
                            if adx_value >= 50:
                                indicators['adx_trend'] = "极强趋势"
                            elif adx_value >= 25:
                                indicators['adx_trend'] = "强趋势"
                            else:
                                indicators['adx_trend'] = "弱趋势或震荡"
            
            # 抛物线SAR
            if len(df) >= 10:
                if PANDAS_TA_AVAILABLE:
                    sar = ta.psar(high, low, close)
                    if sar is not None and not sar.empty:
                        indicators['sar'] = float(sar.iloc[-1])
                        current_price = close.iloc[-1]
                        if current_price > indicators['sar']:
                            indicators['sar_signal'] = "看涨"
                        else:
                            indicators['sar_signal'] = "看跌"
        
        except Exception as e:
            logger.warning(f"计算趋势指标失败: {str(e)}")
        
        return indicators
    
    def _calculate_volume_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算成交量指标"""
        indicators = {}
        
        try:
            close = df['close']
            volume = df['volume_usd']
            
            # 成交量移动平均
            if len(df) >= 20:
                volume_ma = volume.rolling(window=20).mean()
                if not volume_ma.empty:
                    current_volume = volume.iloc[-1]
                    indicators['volume_ma_20'] = float(volume_ma.iloc[-1])
                    indicators['volume_ratio'] = float(current_volume / volume_ma.iloc[-1]) if volume_ma.iloc[-1] != 0 else 1
                    
                    # 成交量信号
                    if indicators['volume_ratio'] >= 2:
                        indicators['volume_signal'] = "异常放量"
                    elif indicators['volume_ratio'] >= 1.5:
                        indicators['volume_signal'] = "明显放量"
                    elif indicators['volume_ratio'] <= 0.5:
                        indicators['volume_signal'] = "缩量"
                    else:
                        indicators['volume_signal'] = "正常"
            
            # OBV (能量潮)
            if PANDAS_TA_AVAILABLE and len(df) >= 10:
                obv = ta.obv(close, volume)
                if obv is not None and not obv.empty:
                    indicators['obv'] = float(obv.iloc[-1])
                    
                    # OBV趋势
                    if len(obv) >= 5:
                        obv_trend = obv.iloc[-1] - obv.iloc[-5]
                        if obv_trend > 0:
                            indicators['obv_trend'] = "资金流入"
                        elif obv_trend < 0:
                            indicators['obv_trend'] = "资金流出"
                        else:
                            indicators['obv_trend'] = "资金平衡"
        
        except Exception as e:
            logger.warning(f"计算成交量指标失败: {str(e)}")
        
        return indicators
    
    def _calculate_rsi(self, close: pd.Series, period: int = 14) -> pd.Series:
        """手动计算RSI（当pandas_ta不可用时）"""
        try:
            delta = close.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series()
    
    def _calculate_macd(self, close: pd.Series, fast: int, slow: int, signal: int) -> Dict[str, float]:
        """手动计算MACD（当pandas_ta不可用时）"""
        try:
            ema_fast = close.ewm(span=fast).mean()
            ema_slow = close.ewm(span=slow).mean()
            macd = ema_fast - ema_slow
            signal = macd.ewm(span=signal).mean()
            histogram = macd - signal
            
            return {
                f'macd_{fast}_{slow}_{signal}': float(macd.iloc[-1]),
                f'macd_signal_{fast}_{slow}_{signal}': float(signal.iloc[-1]),
                f'macd_histogram_{fast}_{slow}_{signal}': float(histogram.iloc[-1]),
                f'macd_trend_{fast}_{slow}_{signal}': "看涨" if macd.iloc[-1] > signal.iloc[-1] else "看跌"
            }
        except:
            return {}
    
    def _calculate_bollinger_bands(self, close: pd.Series, period: int = 20, std: float = 2) -> Dict[str, float]:
        """手动计算布林带（当pandas_ta不可用时）"""
        try:
            ma = close.rolling(window=period).mean()
            std_dev = close.rolling(window=period).std()
            
            upper = ma + (std_dev * std)
            lower = ma - (std_dev * std)
            
            current_price = close.iloc[-1]
            bb_width = upper.iloc[-1] - lower.iloc[-1]
            
            result = {
                f'bb_upper_{period}': float(upper.iloc[-1]),
                f'bb_middle_{period}': float(ma.iloc[-1]),
                f'bb_lower_{period}': float(lower.iloc[-1])
            }
            
            if bb_width > 0:
                result[f'bb_position_{period}'] = float((current_price - lower.iloc[-1]) / bb_width * 100)
            
            # 布林带信号
            if current_price >= upper.iloc[-1]:
                result[f'bb_signal_{period}'] = "超买(触及上轨)"
            elif current_price <= lower.iloc[-1]:
                result[f'bb_signal_{period}'] = "超卖(触及下轨)"
            else:
                result[f'bb_signal_{period}'] = "正常区间"
            
            return result
        except:
            return {}
    
    def get_trading_signals(self, indicators: Dict[str, Any]) -> Dict[str, str]:
        """综合分析生成交易信号"""
        signals = {}
        
        try:
            bullish_signals = 0
            bearish_signals = 0
            
            # RSI信号
            for period in self.rsi_periods:
                if f'rsi_signal_{period}' in indicators:
                    if indicators[f'rsi_signal_{period}'] == "超卖":
                        bullish_signals += 1
                        signals[f'rsi_analysis_{period}'] = f"RSI超卖，可能反弹"
                    elif indicators[f'rsi_signal_{period}'] == "超买":
                        bearish_signals += 1
                        signals[f'rsi_analysis_{period}'] = f"RSI超买，可能回调"
                    else:
                        signals[f'rsi_analysis_{period}'] = "RSI中性"
            
            # MACD信号
            for config in self.macd_configs:
                if f'macd_trend_{config["fast"]}_{config["slow"]}_{config["signal"]}' in indicators:
                    if indicators[f'macd_trend_{config["fast"]}_{config["slow"]}_{config["signal"]}'] == "看涨":
                        bullish_signals += 1
                        signals[f'macd_analysis_{config["fast"]}_{config["slow"]}_{config["signal"]}'] = "MACD金叉，看涨信号"
                    else:
                        bearish_signals += 1
                        signals[f'macd_analysis_{config["fast"]}_{config["slow"]}_{config["signal"]}'] = "MACD死叉，看跌信号"
            
            # 布林带信号
            for period in self.bollinger_periods:
                if f'bb_signal_{period}' in indicators:
                    if "超卖" in indicators[f'bb_signal_{period}']:
                        bullish_signals += 1
                        signals[f'bb_analysis_{period}'] = f"价格触及布林带下轨，可能反弹"
                    elif "超买" in indicators[f'bb_signal_{period}']:
                        bearish_signals += 1
                        signals[f'bb_analysis_{period}'] = f"价格触及布林带上轨，可能回调"
                    else:
                        signals[f'bb_analysis_{period}'] = "价格在布林带正常区间"
            
            # 移动平均线信号
            ma_signals = []
            for period in self.ma_periods:
                ma_key = f'ma_{period}_diff_percent'
                if ma_key in indicators:
                    diff_percent = indicators[ma_key]
                    if diff_percent > 2:
                        bullish_signals += 0.5
                        ma_signals.append(f"价格高于{period}日均线{abs(diff_percent):.1f}%")
                    elif diff_percent < -2:
                        bearish_signals += 0.5
                        ma_signals.append(f"价格低于{period}日均线{abs(diff_percent):.1f}%")
            
            if ma_signals:
                signals['ma_analysis'] = "；".join(ma_signals)
            else:
                signals['ma_analysis'] = "价格接近主要移动平均线"
            
            # 基差信号分析（合约市场）
            if 'current_basis' in indicators:
                current_basis = indicators['current_basis']
                basis_sentiment = indicators.get('basis_sentiment', '未知')
                arbitrage_opportunity = indicators.get('arbitrage_opportunity', '未知')
                
                # 基差对整体信号的影响
                if current_basis > 0.05:  # 正基差超过0.05%
                    bullish_signals += 0.3  # 轻微看涨影响
                    signals['basis_analysis'] = f"期现基差{current_basis:.4f}%，显示市场乐观情绪，支持看涨"
                elif current_basis < -0.05:  # 负基差超过-0.05%
                    bearish_signals += 0.3  # 轻微看跌影响
                    signals['basis_analysis'] = f"期现基差{current_basis:.4f}%，显示市场悲观情绪，支持看跌"
                else:
                    signals['basis_analysis'] = f"期现基差{current_basis:.4f}%，市场情绪中性，对方向判断影响有限"
                
                # 套利机会提示
                if arbitrage_opportunity == "高":
                    signals['arbitrage_note'] = "存在较好的期现套利机会，基差波动较大"
                elif arbitrage_opportunity == "中等":
                    signals['arbitrage_note'] = "存在一定的套利机会，可关注基差变化"
            
            # 综合信号
            if bullish_signals > bearish_signals:
                signals['overall_signal'] = "偏向看涨"
                signals['signal_strength'] = f"看涨信号{bullish_signals}个，看跌信号{bearish_signals}个"
            elif bearish_signals > bullish_signals:
                signals['overall_signal'] = "偏向看跌"
                signals['signal_strength'] = f"看跌信号{bearish_signals}个，看涨信号{bullish_signals}个"
            else:
                signals['overall_signal'] = "中性震荡"
                signals['signal_strength'] = "看涨看跌信号相当"
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {str(e)}")
            signals['overall_signal'] = "信号分析失败"
        
        return signals
    
    def _add_compatibility_indicators(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """添加向后兼容的指标别名"""
        compat = {}
        
        try:
            # RSI兼容性（使用14周期作为默认）
            if 'rsi_14' in indicators:
                compat['rsi'] = indicators['rsi_14']
                if 'rsi_signal_14' in indicators:
                    compat['rsi_signal'] = indicators['rsi_signal_14']
            
            # MACD兼容性（使用标准12,26,9作为默认）
            if 'macd_12_26_9' in indicators:
                compat['macd'] = indicators['macd_12_26_9']
                if 'macd_signal_12_26_9' in indicators:
                    compat['macd_signal'] = indicators['macd_signal_12_26_9']
                if 'macd_histogram_12_26_9' in indicators:
                    compat['macd_histogram'] = indicators['macd_histogram_12_26_9']
                if 'macd_trend_12_26_9' in indicators:
                    compat['macd_trend'] = indicators['macd_trend_12_26_9']
            
            # 布林带兼容性（使用20周期作为默认）
            if 'bb_upper_20' in indicators:
                compat['bb_upper'] = indicators['bb_upper_20']
            if 'bb_middle_20' in indicators:
                compat['bb_middle'] = indicators['bb_middle_20']
            if 'bb_lower_20' in indicators:
                compat['bb_lower'] = indicators['bb_lower_20']
            if 'bb_position_20' in indicators:
                compat['bb_position'] = indicators['bb_position_20']
            if 'bb_signal_20' in indicators:
                compat['bb_signal'] = indicators['bb_signal_20']
            
            # 移动平均线兼容性
            if 'ma_20' in indicators:
                compat['ma'] = indicators['ma_20']
            if 'ema_20' in indicators:
                compat['ema'] = indicators['ema_20']
            
        except Exception as e:
            logger.warning(f"添加兼容性指标失败: {str(e)}")
        
        return compat
    
    def get_multi_period_summary(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """获取多周期技术指标摘要"""
        summary = {
            'bollinger_bands': {},
            'moving_averages': {},
            'rsi_indicators': {},
            'macd_indicators': {}
        }
        
        try:
            # 布林带摘要
            for period in self.bollinger_periods:
                if f'bb_signal_{period}' in indicators:
                    summary['bollinger_bands'][f'bb_{period}'] = {
                        'signal': indicators[f'bb_signal_{period}'],
                        'position': indicators.get(f'bb_position_{period}', 0),
                        'upper': indicators.get(f'bb_upper_{period}', 0),
                        'lower': indicators.get(f'bb_lower_{period}', 0)
                    }
            
            # 移动平均线摘要
            current_price = indicators.get('current_price', 0)
            for period in self.ma_periods:
                if f'ma_{period}' in indicators:
                    ma_value = indicators[f'ma_{period}']
                    summary['moving_averages'][f'ma_{period}'] = {
                        'value': ma_value,
                        'diff_percent': indicators.get(f'ma_{period}_diff_percent', 0),
                        'position': 'above' if current_price > ma_value else 'below'
                    }
            
            # RSI摘要
            for period in self.rsi_periods:
                if f'rsi_{period}' in indicators:
                    summary['rsi_indicators'][f'rsi_{period}'] = {
                        'value': indicators[f'rsi_{period}'],
                        'signal': indicators.get(f'rsi_signal_{period}', 'unknown')
                    }
            
            # MACD摘要
            for config in self.macd_configs:
                key = f"{config['fast']}_{config['slow']}_{config['signal']}"
                if f'macd_{key}' in indicators:
                    summary['macd_indicators'][f'macd_{key}'] = {
                        'macd': indicators[f'macd_{key}'],
                        'signal': indicators.get(f'macd_signal_{key}', 0),
                        'histogram': indicators.get(f'macd_histogram_{key}', 0),
                        'trend': indicators.get(f'macd_trend_{key}', 'unknown')
                    }
            
        except Exception as e:
            logger.error(f"生成多周期摘要失败: {str(e)}")
        
        return summary


def test_technical_analysis():
    """测试技术分析功能"""
    # 创建示例数据
    import numpy as np
    dates = pd.date_range('2024-01-01', periods=100, freq='H')
    
    # 生成模拟价格数据
    np.random.seed(42)
    close_prices = 50000 + np.cumsum(np.random.randn(100) * 100)
    
    # 生成OHLCV数据
    df = pd.DataFrame({
        'open': close_prices * (1 + np.random.randn(100) * 0.001),
        'high': close_prices * (1 + np.abs(np.random.randn(100)) * 0.01),
        'low': close_prices * (1 - np.abs(np.random.randn(100)) * 0.01),
        'close': close_prices,
        'volume_usd': np.random.rand(100) * 1000000
    }, index=dates)
    
    # 测试技术分析
    analyzer = AITechnicalAnalysis()
    indicators = analyzer.calculate_all_indicators(df)
    
    print("=== 技术指标计算结果 ===")
    for key, value in indicators.items():
        if isinstance(value, float):
            print(f"{key}: {value:.4f}")
        else:
            print(f"{key}: {value}")
    
    # 测试交易信号
    signals = analyzer.get_trading_signals(indicators)
    print("\n=== 交易信号分析 ===")
    for key, value in signals.items():
        print(f"{key}: {value}")


if __name__ == "__main__":
    test_technical_analysis() 