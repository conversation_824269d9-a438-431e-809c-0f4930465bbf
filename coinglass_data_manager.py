#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CoinGlass数据管理器
实现完整的分页请求和缓存机制，正确使用open_interest_change_percent_5m字段
"""

import os
import json
import time
import requests
import asyncio
import aiohttp
import threading
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor

# CoinGlass API配置
CG_API_KEY = "c40fbbee201d4dfab3a4b62f37f0b610"

class CoinGlassDataManager:
    """CoinGlass数据管理器 - 支持分页获取完整数据"""
    
    def __init__(self, cache_dir: str):
        self.cache_dir = cache_dir
        self.coinglass_cache_dir = os.path.join(cache_dir)
        self.headers = {
            "accept": "application/json",
            "CG-API-KEY": CG_API_KEY
        }
        self._executor = ThreadPoolExecutor(max_workers=2)
        self._update_lock = threading.Lock()
        self._is_updating = False
        self._scheduler_thread = None
        self._scheduler_running = False
        
        # 确保缓存目录存在
        os.makedirs(self.coinglass_cache_dir, exist_ok=True)
        print(f"✅ CoinGlass数据管理器初始化完成，缓存目录: {self.coinglass_cache_dir}")
    
    def get_beijing_time(self) -> datetime:
        """获取北京时间"""
        return datetime.now()
    
    def get_cache_timestamp_folder(self) -> str:
        """获取缓存时间戳文件夹名"""
        return self.get_beijing_time().strftime("%Y%m%d_%H%M%S")
    
    def fetch_futures_data(self) -> Tuple[bool, Optional[List[Dict]], str]:
        """获取合约数据（分页循环获取所有数据，只使用Binance交易所）"""
        try:
            print("🔄 正在获取CoinGlass合约数据（分页循环模式，只使用Binance）...")
            base_url = "https://open-api-v4.coinglass.com/api/futures/coins-markets"
            
            all_data = []
            page = 1
            
            while True:
                # 🔧 只使用Binance交易所
                params = {
                    "page": page,
                    "per_page": 100,  # 每页最大100条记录
                    "exchange_list": "Binance"
                }
                
                response = requests.get(base_url, headers=self.headers, params=params, timeout=30)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == "0" and "data" in data:
                        page_data = data["data"]
                        if len(page_data) > 0:
                            all_data.extend(page_data)  # 累积所有页面数据
                            print(f"   合约API页码{page}: 获取{len(page_data)}个币种")
                            page += 1  # 继续下一页
                            time.sleep(0.1)  # 避免API限制
                        else:
                            break  # 没有更多数据，退出循环
                    else:
                        error_msg = f"合约API错误: {data}"
                        print(error_msg)
                        return False, None, error_msg
                else:
                    error_msg = f"合约API HTTP错误: {response.status_code}"
                    print(error_msg)
                    return False, None, error_msg
            
            print(f"✅ 合约API总计获取: {len(all_data)}个币种")
            return True, all_data, "获取成功"
            
        except Exception as e:
            error_msg = f"合约数据获取异常: {e}"
            print(error_msg)
            return False, None, error_msg
    
    def fetch_spot_data(self) -> Tuple[bool, Optional[List[Dict]], str]:
        """获取现货数据（分页循环获取所有数据，只使用Binance交易所）"""
        try:
            print("🔄 正在获取CoinGlass现货数据（分页循环模式，只使用Binance）...")
            base_url = "https://open-api-v4.coinglass.com/api/spot/coins-markets"
            
            all_data = []
            page = 1
            
            while True:
                # 🔧 只使用Binance交易所
                params = {
                    "page": page,
                    "per_page": 100,  # 每页最大100条记录
                    "exchange_list": "Binance"
                }
                
                response = requests.get(base_url, headers=self.headers, params=params, timeout=30)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == "0" and "data" in data:
                        page_data = data["data"]
                        if len(page_data) > 0:
                            all_data.extend(page_data)  # 累积所有页面数据
                            print(f"   现货API页码{page}: 获取{len(page_data)}个币种")
                            page += 1  # 继续下一页
                            time.sleep(0.1)  # 避免API限制
                        else:
                            break  # 没有更多数据，退出循环
                    else:
                        error_msg = f"现货API错误: {data}"
                        print(error_msg)
                        return False, None, error_msg
                else:
                    error_msg = f"现货API HTTP错误: {response.status_code}, {response.text}"
                    print(error_msg)
                    return False, None, error_msg
            
            print(f"✅ 现货API总计获取: {len(all_data)}个币种")
            return True, all_data, "获取成功"
            
        except Exception as e:
            error_msg = f"现货数据获取异常: {e}"
            print(error_msg)
            return False, None, error_msg

    def fetch_rsi_data(self) -> Tuple[bool, Optional[List[Dict]], str]:
        """获取RSI列表数据"""
        try:
            print("🔄 正在获取CoinGlass RSI数据...")
            url = "https://open-api-v4.coinglass.com/api/futures/rsi/list"

            response = requests.get(url, headers=self.headers, timeout=30)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "0" and "data" in data:
                    rsi_data = data["data"]
                    print(f"✅ RSI API获取: {len(rsi_data)}个币种")
                    return True, rsi_data, "获取成功"
                else:
                    error_msg = f"RSI API错误: {data}"
                    print(error_msg)
                    return False, None, error_msg
            else:
                error_msg = f"RSI API HTTP错误: {response.status_code}, {response.text}"
                print(error_msg)
                return False, None, error_msg

        except Exception as e:
            error_msg = f"RSI数据获取异常: {e}"
            print(error_msg)
            return False, None, error_msg

    async def fetch_futures_data_async(self) -> Tuple[bool, Optional[List[Dict]], str]:
        """异步获取合约数据"""
        try:
            print("🔄 正在异步获取CoinGlass合约数据（分页循环模式）...")
            all_data = []
            page = 1
            
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(timeout=timeout, headers=self.headers) as session:
                while True:
                    try:
                        # 🔧 使用正确的API端点，只使用Binance交易所
                        url = f"https://open-api-v4.coinglass.com/api/futures/coins-markets"
                        params = {
                            "page": page,
                            "per_page": 100,
                            "exchange_list": "Binance"
                        }
                        
                        async with session.get(url, params=params) as response:
                            if response.status == 200:
                                data = await response.json()
                                if data.get("code") == "0" and "data" in data:
                                    items = data["data"]
                                    
                                    if not items:
                                        break  # 没有更多数据
                                    
                                    all_data.extend(items)
                                    print(f"   合约API页码{page}: 获取{len(items)}个币种")
                                    page += 1
                                    
                                    # 🔧 关键：添加小延迟避免API限制
                                    await asyncio.sleep(0.1)
                                else:
                                    print(f"合约API错误: {data}")
                                    break
                            else:
                                print(f"合约API页码{page}请求失败: {response.status}")
                                break
                                
                    except Exception as e:
                        print(f"获取合约数据页码{page}异常: {e}")
                        break
            
            print(f"✅ 合约API总计获取: {len(all_data)}个币种")
            return True, all_data, f"成功获取{len(all_data)}个合约币种数据"
            
        except Exception as e:
            print(f"异步获取合约数据失败: {e}")
            return False, None, f"获取合约数据失败: {e}"
    
    def save_cache_new(self, futures_data: Optional[List[Dict]] = None,
                       spot_data: Optional[List[Dict]] = None,
                       rsi_data: Optional[List[Dict]] = None) -> bool:
        """保存缓存到时间戳文件夹（支持4文件存储：futures.json, spot.json, rsi.json, cache_info.json）"""
        try:
            # 创建时间戳文件夹
            timestamp_folder = self.get_cache_timestamp_folder()
            cache_folder_path = os.path.join(self.coinglass_cache_dir, timestamp_folder)
            os.makedirs(cache_folder_path, exist_ok=True)

            # 保存数据
            if futures_data is not None:
                futures_file = os.path.join(cache_folder_path, "futures.json")
                with open(futures_file, 'w', encoding='utf-8') as f:
                    json.dump(futures_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 合约数据已保存: {futures_file}")

            if spot_data is not None:
                spot_file = os.path.join(cache_folder_path, "spot.json")
                with open(spot_file, 'w', encoding='utf-8') as f:
                    json.dump(spot_data, f, ensure_ascii=False, indent=2)
                print(f"✅ 现货数据已保存: {spot_file}")

            if rsi_data is not None:
                rsi_file = os.path.join(cache_folder_path, "rsi.json")
                with open(rsi_file, 'w', encoding='utf-8') as f:
                    json.dump(rsi_data, f, ensure_ascii=False, indent=2)
                print(f"✅ RSI数据已保存: {rsi_file}")

            # 保存缓存信息
            cache_info = {
                "timestamp": self.get_beijing_time().isoformat(),
                "futures_count": len(futures_data) if futures_data else 0,
                "spot_count": len(spot_data) if spot_data else 0,
                "rsi_count": len(rsi_data) if rsi_data else 0,
                "total_count": (len(futures_data) if futures_data else 0) + (len(spot_data) if spot_data else 0) + (len(rsi_data) if rsi_data else 0)
            }
            
            cache_info_file = os.path.join(cache_folder_path, "cache_info.json")
            with open(cache_info_file, 'w', encoding='utf-8') as f:
                json.dump(cache_info, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 缓存保存成功: {cache_folder_path}")
            
            # 清理旧缓存
            self.cleanup_old_cache_folders()
            
            return True
            
        except Exception as e:
            print(f"❌ 缓存保存失败: {e}")
            return False
    
    def cleanup_old_cache_folders(self, keep_count: int = 60):
        """清理旧缓存文件夹，保留最新的几个"""
        try:
            if not os.path.exists(self.coinglass_cache_dir):
                return
            
            # 获取所有时间戳文件夹
            folders = []
            for item in os.listdir(self.coinglass_cache_dir):
                folder_path = os.path.join(self.coinglass_cache_dir, item)
                if os.path.isdir(folder_path) and item.replace('_', '').isdigit():
                    folders.append((item, folder_path))
            
            # 按时间戳排序（最新的在前）
            folders.sort(key=lambda x: x[0], reverse=True)
            
            # 删除多余的文件夹
            cleaned_count = 0
            for folder_name, folder_path in folders[keep_count:]:
                try:
                    import shutil
                    shutil.rmtree(folder_path)
                    cleaned_count += 1
                except Exception as e:
                    print(f"⚠️ 清理文件夹失败 {folder_path}: {e}")
            
            if cleaned_count > 0:
                print(f"🧹 清理了 {cleaned_count} 个旧缓存文件夹")
                
        except Exception as e:
            print(f"⚠️ 清理缓存文件夹失败: {e}")
    
    def get_latest_cache_data(self) -> Optional[Dict[str, Any]]:
        """获取最新的缓存数据"""
        try:
            if not os.path.exists(self.coinglass_cache_dir):
                return None
            
            # 获取所有时间戳文件夹
            folders = []
            for item in os.listdir(self.coinglass_cache_dir):
                folder_path = os.path.join(self.coinglass_cache_dir, item)
                if os.path.isdir(folder_path) and item.replace('_', '').isdigit():
                    folders.append((item, folder_path))
            
            if not folders:
                return None
            
            # 按时间戳排序，获取最新的
            folders.sort(key=lambda x: x[0], reverse=True)
            latest_folder = folders[0][1]
            
            # 读取数据
            result = {}
            
            futures_file = os.path.join(latest_folder, "futures.json")
            if os.path.exists(futures_file):
                with open(futures_file, 'r', encoding='utf-8') as f:
                    result["futures"] = json.load(f)
            
            spot_file = os.path.join(latest_folder, "spot.json")
            if os.path.exists(spot_file):
                with open(spot_file, 'r', encoding='utf-8') as f:
                    result["spot"] = json.load(f)

            rsi_file = os.path.join(latest_folder, "rsi.json")
            if os.path.exists(rsi_file):
                with open(rsi_file, 'r', encoding='utf-8') as f:
                    result["rsi"] = json.load(f)

            cache_info_file = os.path.join(latest_folder, "cache_info.json")
            if os.path.exists(cache_info_file):
                with open(cache_info_file, 'r', encoding='utf-8') as f:
                    result["cache_info"] = json.load(f)
            
            return result
            
        except Exception as e:
            print(f"❌ 获取最新缓存数据失败: {e}")
            return None
    
    def update_all_cache(self) -> Dict[str, bool]:
        """更新所有缓存数据"""
        # 防止重复更新
        with self._update_lock:
            if self._is_updating:
                print("⚠️ 缓存更新已在进行中，跳过此次更新")
                return {
                    'futures': False,
                    'spot': False,
                    'rsi': False,
                    'cache_saved': False,
                    'all_success': False,
                    'message': '更新已在进行中'
                }
            self._is_updating = True
        
        try:
            print("🔄 开始更新CoinGlass缓存数据...")

            # 获取合约数据
            futures_success, futures_data, futures_msg = self.fetch_futures_data()
            print(f"合约数据获取: {'✅' if futures_success else '❌'} {futures_msg}")

            # 获取现货数据
            spot_success, spot_data, spot_msg = self.fetch_spot_data()
            print(f"现货数据获取: {'✅' if spot_success else '❌'} {spot_msg}")

            # 获取RSI数据
            rsi_success, rsi_data, rsi_msg = self.fetch_rsi_data()
            print(f"RSI数据获取: {'✅' if rsi_success else '❌'} {rsi_msg}")

            # 保存缓存
            cache_saved = False
            if futures_success or spot_success or rsi_success:
                cache_saved = self.save_cache_new(
                    futures_data if futures_success else None,
                    spot_data if spot_success else None,
                    rsi_data if rsi_success else None
                )

            return {
                'futures': futures_success,
                'spot': spot_success,
                'rsi': rsi_success,
                'cache_saved': cache_saved,
                'all_success': futures_success and spot_success and rsi_success and cache_saved
            }
            
        finally:
            with self._update_lock:
                self._is_updating = False
    
    def _scheduled_update(self):
        """定时更新任务"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n🕐 [{current_time}] 开始定时更新CoinGlass缓存...")
            
            result = self.update_all_cache()
            
            if result['all_success']:
                print(f"✅ [{current_time}] 定时更新完成")
            else:
                print(f"⚠️ [{current_time}] 定时更新部分失败: {result}")
                
        except Exception as e:
            print(f"❌ 定时更新异常: {e}")
    
    def start_scheduled_updates(self, interval_minutes: int = 1):
        """启动定时更新调度器"""
        if self._scheduler_running:
            print("⚠️ 定时调度器已在运行中")
            return
        
        print(f"🚀 启动CoinGlass缓存定时更新，间隔: {interval_minutes}分钟")
        
        # 清除之前的调度
        schedule.clear()
        
        # 设置定时任务
        schedule.every(interval_minutes).minutes.do(self._scheduled_update)
        
        # 启动调度器线程
        self._scheduler_running = True
        self._scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self._scheduler_thread.start()
        
        print(f"✅ 定时调度器已启动，每{interval_minutes}分钟更新一次缓存")
    
    def _run_scheduler(self):
        """运行调度器"""
        while self._scheduler_running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                print(f"❌ 调度器异常: {e}")
                time.sleep(5)
    
    def stop_scheduled_updates(self):
        """停止定时更新调度器"""
        if not self._scheduler_running:
            print("⚠️ 定时调度器未在运行")
            return
        
        print("🛑 停止CoinGlass缓存定时更新...")
        self._scheduler_running = False
        
        # 清除调度任务
        schedule.clear()
        
        # 等待线程结束
        if self._scheduler_thread and self._scheduler_thread.is_alive():
            self._scheduler_thread.join(timeout=5)
        
        print("✅ 定时调度器已停止")
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            "running": self._scheduler_running,
            "thread_alive": self._scheduler_thread.is_alive() if self._scheduler_thread else False,
            "scheduled_jobs": len(schedule.jobs),
            "next_run": str(schedule.next_run()) if schedule.jobs else None,
            "is_updating": self._is_updating
        }

def extract_base_symbol(symbol: str) -> str:
    """从交易对中提取基础币种名称
    
    Args:
        symbol: 交易对符号，如 BTCUSDT, BTC/USDT, ETH-USDT 等
        
    Returns:
        基础币种名称，如 BTC, ETH, SOL 等
    """
    if not symbol:
        return symbol
    
    # 移除常见的交易对后缀
    symbol = symbol.upper().strip()
    
    # 处理不同格式的交易对
    for suffix in ['USDT', '/USDT', '-USDT', '_USDT']:
        if symbol.endswith(suffix):
            return symbol[:-len(suffix)]
    
    # 如果没有匹配的后缀，返回原始符号
    return symbol

class CoinGlassOpenInterestMonitor:
    """基于CoinGlass API的持仓量监控器"""
    
    def __init__(self, cache_dir: str, auto_start_scheduler: bool = True):
        self.data_manager = CoinGlassDataManager(cache_dir)
        self.threshold = 5.0   # 5%阈值
        self.cooldown_period = 1800   # 30分钟冷却期
        self.alert_cache = {}  # 警报缓存
        
        print(f"💰 CoinGlass持仓量监控器初始化完成")
        print(f"📊 监控阈值: open_interest_change_percent_5m ≥ {self.threshold}%")
        print(f"⏰ 冷却期: {self.cooldown_period}秒")
        
        # 自动启动定时更新调度器
        if auto_start_scheduler:
            self.data_manager.start_scheduled_updates(interval_minutes=1)
            print("🚀 已启动CoinGlass缓存自动更新（每1分钟）")
    
    def check_open_interest_signals(self, force_update: bool = False) -> List[Dict]:
        """检查持仓量信号 - 使用CoinGlass API的open_interest_change_percent_5m字段"""
        try:
            print("🔍 检查持仓量信号...")
            
            # 获取最新数据
            if force_update:
                print("🔄 强制更新数据...")
                self.data_manager.update_all_cache()
            
            cache_data = self.data_manager.get_latest_cache_data()
            if not cache_data or "futures" not in cache_data:
                print("❌ 没有可用的缓存数据，尝试立即更新...")
                # 如果没有缓存数据，立即更新一次
                update_result = self.data_manager.update_all_cache()
                if update_result['all_success']:
                    cache_data = self.data_manager.get_latest_cache_data()
                else:
                    print("❌ 更新缓存失败")
                    return []
            
            if not cache_data or "futures" not in cache_data:
                print("❌ 仍然没有可用的缓存数据")
                return []
            
            futures_data = cache_data["futures"]
            
            # 显示缓存数据的时间戳信息
            cache_info = cache_data.get("cache_info", {})
            cache_timestamp = cache_info.get("timestamp", "未知")
            print(f"📊 分析 {len(futures_data)} 个币种的持仓量数据...")
            print(f"📅 缓存数据时间: {cache_timestamp}")
            
            signals = []
            current_time = time.time()
            
            for coin in futures_data:
                try:
                    symbol = coin.get("symbol", "")
                    if not symbol:
                        continue
                    
                    # 🔧 关键：直接使用CoinGlass API的open_interest_change_percent_5m字段
                    oi_change_5m = coin.get("open_interest_change_percent_5m")
                    
                    if oi_change_5m is None:
                        continue
                    
                    # 转换为数值
                    oi_change_5m = float(oi_change_5m)
                    
                    # 检查是否满足阈值条件 (绝对值>=5%)
                    if abs(oi_change_5m) >= self.threshold:
                        # 🔧 修复缓存键生成逻辑：按币种名称+信号类型冷却，允许不同信号类型独立发送
                        base_symbol = extract_base_symbol(symbol)
                        alert_key = f"{base_symbol}_trend"
                        if alert_key in self.alert_cache:
                            last_alert_time = self.alert_cache[alert_key]
                            if current_time - last_alert_time < self.cooldown_period:
                                continue  # 还在冷却期内
                        
                        # 生成信号
                        direction = "增长" if oi_change_5m > 0 else "减少"
                        emoji = "📈" if oi_change_5m > 0 else "📉"
                        
                        signal = {
                            "type": "open_interest",
                            "symbol": symbol,
                            "signal": f"持仓量{direction}",
                            "value": oi_change_5m,
                            "threshold": self.threshold,
                            "current_price": coin.get("current_price", 0),
                            "open_interest_usd": coin.get("open_interest_usd", 0),
                            "timestamp": datetime.now().isoformat(),
                            "message": f"{emoji} {symbol} 持仓量5分钟{direction}: {oi_change_5m:.2f}%"
                        }
                        
                        signals.append(signal)
                        
                        # 记录到缓存
                        self.alert_cache[alert_key] = current_time
                        
                        print(f"🚨 持仓量信号: {signal['message']}")
                        
                except Exception as e:
                    print(f"⚠️ 处理币种 {symbol} 失败: {e}")
                    continue
            
            print(f"✅ 持仓量信号检查完成，发现 {len(signals)} 个信号")
            return signals
            
        except Exception as e:
            print(f"❌ 持仓量信号检查失败: {e}")
            return []
    
    def get_cache_status(self) -> Dict[str, Any]:
        """获取缓存状态"""
        try:
            cache_data = self.data_manager.get_latest_cache_data()
            if not cache_data:
                return {"status": "no_cache", "message": "没有缓存数据"}
            
            cache_info = cache_data.get("cache_info", {})
            scheduler_status = self.data_manager.get_scheduler_status()
            
            return {
                "status": "ok",
                "timestamp": cache_info.get("timestamp", "未知"),
                "futures_count": cache_info.get("futures_count", 0),
                "spot_count": cache_info.get("spot_count", 0),
                "total_count": cache_info.get("total_count", 0),
                "scheduler": scheduler_status
            }
            
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    def stop_scheduler(self):
        """停止定时更新调度器"""
        self.data_manager.stop_scheduled_updates()
    
    def restart_scheduler(self, interval_minutes: int = 1):
        """重启定时更新调度器"""
        self.data_manager.stop_scheduled_updates()
        time.sleep(1)
        self.data_manager.start_scheduled_updates(interval_minutes)

def main():
    """测试函数"""
    print("🚀 CoinGlass持仓量监控器测试")
    print("=" * 60)
    
    # 创建监控器（自动启动定时更新）
    # 使用相对路径：data\coinglass
    cache_dir = r"data\coinglass"
    monitor = CoinGlassOpenInterestMonitor(cache_dir, auto_start_scheduler=True)
    
    # 检查缓存状态
    cache_status = monitor.get_cache_status()
    print(f"📋 缓存状态: {cache_status}")
    
    # 立即更新一次数据
    print("\n🔄 立即更新CoinGlass数据...")
    update_result = monitor.data_manager.update_all_cache()
    print(f"更新结果: {update_result}")
    
    # 检查信号
    print("\n🔍 检查持仓量信号...")
    signals = monitor.check_open_interest_signals()
    
    if signals:
        print(f"\n🚨 发现 {len(signals)} 个持仓量信号:")
        for signal in signals[:5]:  # 显示前5个
            print(f"   - {signal['message']}")
    else:
        print("📊 暂无持仓量信号")
    
    # 显示调度器状态
    print(f"\n📊 调度器状态: {monitor.data_manager.get_scheduler_status()}")
    
    # 运行一段时间来观察定时更新
    print("\n⏰ 监控定时更新（运行60秒）...")
    try:
        time.sleep(60)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    finally:
        # 停止调度器
        monitor.stop_scheduler()
        print("✅ 测试完成，调度器已停止")

if __name__ == "__main__":
    main() 