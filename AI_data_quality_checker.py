#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI数据质量检测器
专门用于检测K线数据的完整性、成交量异常和数据质量问题
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class AIDataQualityChecker:
    """AI数据质量检测器"""
    
    def __init__(self):
        """初始化数据质量检测器"""
        self.quality_thresholds = {
            'volume_low_ratio': 0.2,      # 成交量低于中位数20%视为异常
            'volume_extreme_ratio': 0.1,   # 成交量低于中位数10%视为极端异常
            'incomplete_tolerance': 0.05,   # 允许5%的未完成K线
            'quality_score_threshold': 0.8  # 质量评分阈值
        }
        logger.info("✅ AI数据质量检测器初始化完成")
    
    def comprehensive_quality_check(self, df: pd.DataFrame, interval: str, 
                                  symbol: str) -> Dict[str, Any]:
        """综合数据质量检查
        
        Args:
            df: K线数据DataFrame
            interval: K线周期
            symbol: 交易对符号
            
        Returns:
            质量检查结果字典
        """
        try:
            logger.info(f"🔍 开始对 {symbol} 进行数据质量检查...")
            
            quality_report = {
                'symbol': symbol,
                'interval': interval,
                'total_klines': len(df),
                'check_time': datetime.now().isoformat(),
                'issues': [],
                'warnings': [],
                'recommendations': [],
                'overall_score': 1.0,
                'reliability_level': 'high'
            }
            
            # 1. K线完整性检查
            completeness_result = self._check_kline_completeness(df, interval)
            quality_report.update(completeness_result)
            
            # 2. 成交量异常检查
            volume_result = self._check_volume_anomalies(df)
            quality_report.update(volume_result)
            
            # 3. 数据连续性检查
            continuity_result = self._check_data_continuity(df, interval)
            quality_report.update(continuity_result)
            
            # 4. 价格数据合理性检查
            price_result = self._check_price_reasonableness(df)
            quality_report.update(price_result)
            
            # 5. 计算综合质量评分
            quality_report = self._calculate_overall_quality(quality_report)
            
            # 6. 生成建议和警告
            quality_report = self._generate_recommendations(quality_report)
            
            logger.info(f"✅ {symbol} 数据质量检查完成，评分: {quality_report['overall_score']:.2f}")
            return quality_report
            
        except Exception as e:
            logger.error(f"❌ 数据质量检查失败: {str(e)}")
            return {
                'symbol': symbol,
                'interval': interval,
                'error': str(e),
                'overall_score': 0.0,
                'reliability_level': 'unknown'
            }
    
    def _check_kline_completeness(self, df: pd.DataFrame, interval: str) -> Dict[str, Any]:
        """检查K线完整性"""
        result = {}
        
        try:
            # 检查是否有完整性标记
            if 'is_complete' in df.columns:
                incomplete_count = len(df[~df['is_complete']])
                incomplete_ratio = incomplete_count / len(df)
                
                result['incomplete_klines'] = incomplete_count
                result['incomplete_ratio'] = incomplete_ratio
                
                if incomplete_ratio > self.quality_thresholds['incomplete_tolerance']:
                    result.setdefault('issues', []).append(
                        f"未完成K线比例过高: {incomplete_ratio:.1%} ({incomplete_count}根)"
                    )
                elif incomplete_count > 0:
                    result.setdefault('warnings', []).append(
                        f"存在未完成K线: {incomplete_count}根"
                    )
            
            # 检查最后一根K线的状态
            current_time = datetime.now()
            last_kline_time = df.index[-1]
            interval_seconds = self._get_interval_seconds(interval)
            expected_end_time = last_kline_time + timedelta(seconds=interval_seconds)
            
            if current_time < expected_end_time:
                result.setdefault('warnings', []).append(
                    "当前K线尚未完成，数据可能发生变化"
                )
                result['last_kline_incomplete'] = True
            else:
                result['last_kline_incomplete'] = False
                
        except Exception as e:
            logger.error(f"K线完整性检查失败: {str(e)}")
            result['completeness_check_error'] = str(e)
        
        return result
    
    def _check_volume_anomalies(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检查成交量异常"""
        result = {}
        
        try:
            if 'volume_usd' not in df.columns:
                result['volume_check_error'] = "缺少成交量数据"
                return result
            
            volume_data = df['volume_usd']
            volume_median = volume_data.median()
            volume_mean = volume_data.mean()
            volume_std = volume_data.std()
            
            # 检查异常低成交量
            low_threshold = volume_median * self.quality_thresholds['volume_low_ratio']
            extreme_low_threshold = volume_median * self.quality_thresholds['volume_extreme_ratio']
            
            low_volume_count = len(volume_data[volume_data < low_threshold])
            extreme_low_count = len(volume_data[volume_data < extreme_low_threshold])
            
            result['volume_stats'] = {
                'median': float(volume_median),
                'mean': float(volume_mean),
                'std': float(volume_std),
                'low_volume_count': low_volume_count,
                'extreme_low_count': extreme_low_count
            }
            
            # 当前成交量状态
            current_volume = volume_data.iloc[-1]
            result['current_volume_status'] = self._classify_volume_level(
                current_volume, volume_median
            )
            
            # 生成警告
            if extreme_low_count > 0:
                result.setdefault('issues', []).append(
                    f"极低成交量K线: {extreme_low_count}根，市场流动性严重不足"
                )
            elif low_volume_count > len(df) * 0.3:  # 超过30%的K线成交量偏低
                result.setdefault('warnings', []).append(
                    f"低成交量K线较多: {low_volume_count}根，注意流动性风险"
                )
            
            # 检查当前成交量
            if current_volume < extreme_low_threshold:
                result.setdefault('issues', []).append(
                    "当前成交量极低，价格可能出现异常波动"
                )
            elif current_volume < low_threshold:
                result.setdefault('warnings', []).append(
                    "当前成交量偏低，建议谨慎操作"
                )
                
        except Exception as e:
            logger.error(f"成交量异常检查失败: {str(e)}")
            result['volume_check_error'] = str(e)
        
        return result
    
    def _check_data_continuity(self, df: pd.DataFrame, interval: str) -> Dict[str, Any]:
        """检查数据连续性"""
        result = {}
        
        try:
            # 检查时间间隔是否连续
            time_diffs = df.index.to_series().diff().dropna()
            expected_interval = timedelta(seconds=self._get_interval_seconds(interval))
            
            # 找出时间间隔异常的地方
            abnormal_gaps = time_diffs[time_diffs != expected_interval]
            
            if len(abnormal_gaps) > 0:
                result.setdefault('warnings', []).append(
                    f"数据存在时间间隔异常: {len(abnormal_gaps)}处"
                )
                result['time_gaps'] = len(abnormal_gaps)
            else:
                result['time_gaps'] = 0
                
        except Exception as e:
            logger.error(f"数据连续性检查失败: {str(e)}")
            result['continuity_check_error'] = str(e)
        
        return result
    
    def _check_price_reasonableness(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检查价格数据合理性"""
        result = {}
        
        try:
            # 检查价格数据的基本合理性
            price_columns = ['open', 'high', 'low', 'close']
            
            for col in price_columns:
                if col in df.columns:
                    # 检查是否有负数或零值
                    invalid_prices = df[df[col] <= 0]
                    if len(invalid_prices) > 0:
                        result.setdefault('issues', []).append(
                            f"{col}价格存在无效值: {len(invalid_prices)}个"
                        )
            
            # 检查高低价关系
            if all(col in df.columns for col in ['high', 'low']):
                invalid_hl = df[df['high'] < df['low']]
                if len(invalid_hl) > 0:
                    result.setdefault('issues', []).append(
                        f"最高价低于最低价的异常K线: {len(invalid_hl)}根"
                    )
            
            # 检查价格波动是否异常
            if 'close' in df.columns:
                price_changes = df['close'].pct_change().abs()
                extreme_changes = price_changes[price_changes > 0.5]  # 50%以上的变化
                
                if len(extreme_changes) > 0:
                    result.setdefault('warnings', []).append(
                        f"存在极端价格波动: {len(extreme_changes)}次"
                    )
                    
        except Exception as e:
            logger.error(f"价格合理性检查失败: {str(e)}")
            result['price_check_error'] = str(e)
        
        return result
    
    def _calculate_overall_quality(self, quality_report: Dict[str, Any]) -> Dict[str, Any]:
        """计算综合质量评分"""
        try:
            score = 1.0
            
            # 根据问题数量扣分
            issues_count = len(quality_report.get('issues', []))
            warnings_count = len(quality_report.get('warnings', []))
            
            # 严重问题每个扣0.2分
            score -= issues_count * 0.2
            
            # 警告每个扣0.1分
            score -= warnings_count * 0.1
            
            # 确保评分不低于0
            score = max(0.0, score)
            
            quality_report['overall_score'] = score
            
            # 确定可靠性等级
            if score >= 0.9:
                quality_report['reliability_level'] = 'high'
            elif score >= 0.7:
                quality_report['reliability_level'] = 'medium'
            else:
                quality_report['reliability_level'] = 'low'
                
        except Exception as e:
            logger.error(f"质量评分计算失败: {str(e)}")
            quality_report['overall_score'] = 0.5
            quality_report['reliability_level'] = 'unknown'
        
        return quality_report
    
    def _generate_recommendations(self, quality_report: Dict[str, Any]) -> Dict[str, Any]:
        """生成建议和推荐"""
        try:
            recommendations = []
            
            # 根据可靠性等级生成建议
            reliability = quality_report.get('reliability_level', 'unknown')
            
            if reliability == 'low':
                recommendations.append("数据质量较低，建议等待更多数据确认后再做决策")
                recommendations.append("降低操作仓位，增加风险控制措施")
            elif reliability == 'medium':
                recommendations.append("数据质量中等，建议结合其他指标进行确认")
                recommendations.append("适当降低操作确定性，保持谨慎态度")
            
            # 根据具体问题生成建议
            if quality_report.get('last_kline_incomplete', False):
                recommendations.append("当前K线未完成，建议等待K线收盘后再确认信号")
            
            if quality_report.get('current_volume_status') in ['low', 'extremely_low']:
                recommendations.append("当前成交量偏低，注意流动性风险，避免大额操作")
            
            quality_report['recommendations'] = recommendations
            
        except Exception as e:
            logger.error(f"生成建议失败: {str(e)}")
            quality_report['recommendations'] = ["数据质量分析异常，请谨慎操作"]
        
        return quality_report
    
    def _get_interval_seconds(self, interval: str) -> int:
        """获取K线周期对应的秒数"""
        interval_map = {
            '1m': 60, '3m': 180, '5m': 300, '15m': 900, '30m': 1800,
            '1h': 3600, '4h': 14400, '6h': 21600, '8h': 28800, 
            '12h': 43200, '1d': 86400, '1w': 604800
        }
        return interval_map.get(interval, 3600)
    
    def _classify_volume_level(self, current_volume: float, median_volume: float) -> str:
        """分类成交量等级"""
        ratio = current_volume / median_volume if median_volume > 0 else 1
        
        if ratio < 0.1:
            return 'extremely_low'
        elif ratio < 0.5:
            return 'low'
        elif ratio > 2.0:
            return 'high'
        elif ratio > 5.0:
            return 'extremely_high'
        else:
            return 'normal'
    
    def generate_quality_summary(self, quality_report: Dict[str, Any]) -> str:
        """生成数据质量摘要文本"""
        try:
            symbol = quality_report.get('symbol', 'Unknown')
            score = quality_report.get('overall_score', 0)
            reliability = quality_report.get('reliability_level', 'unknown')
            
            summary = f"📊 {symbol} 数据质量评估: "
            
            if reliability == 'high':
                summary += f"优秀 ({score:.1%}) ✅"
            elif reliability == 'medium':
                summary += f"良好 ({score:.1%}) ⚠️"
            else:
                summary += f"较低 ({score:.1%}) ❌"
            
            # 添加主要问题
            issues = quality_report.get('issues', [])
            if issues:
                summary += f"\n主要问题: {'; '.join(issues[:2])}"
            
            # 添加建议
            recommendations = quality_report.get('recommendations', [])
            if recommendations:
                summary += f"\n建议: {recommendations[0]}"
            
            return summary
            
        except Exception as e:
            logger.error(f"生成质量摘要失败: {str(e)}")
            return "数据质量评估异常"


def test_data_quality_checker():
    """测试数据质量检测器"""
    import numpy as np
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', periods=100, freq='H')
    df = pd.DataFrame({
        'open': np.random.rand(100) * 100 + 50000,
        'high': np.random.rand(100) * 100 + 50100,
        'low': np.random.rand(100) * 100 + 49900,
        'close': np.random.rand(100) * 100 + 50000,
        'volume_usd': np.random.rand(100) * 1000000
    }, index=dates)
    
    # 添加一些异常数据
    df.loc[df.index[-1], 'volume_usd'] = 1000  # 最后一根K线成交量异常低
    df['is_complete'] = True
    df.iloc[-1, df.columns.get_loc('is_complete')] = False  # 最后一根K线未完成
    
    # 测试质量检查
    checker = AIDataQualityChecker()
    result = checker.comprehensive_quality_check(df, '1h', 'BTCUSDT')
    
    print("数据质量检查结果:")
    print(f"总体评分: {result['overall_score']:.2f}")
    print(f"可靠性等级: {result['reliability_level']}")
    print(f"问题: {result.get('issues', [])}")
    print(f"警告: {result.get('warnings', [])}")
    print(f"建议: {result.get('recommendations', [])}")
    
    # 测试摘要生成
    summary = checker.generate_quality_summary(result)
    print(f"\n质量摘要:\n{summary}")


if __name__ == "__main__":
    test_data_quality_checker()
